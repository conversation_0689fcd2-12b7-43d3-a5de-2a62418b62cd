import { withDevtools } from "@middlewares/devtools";
import { withPersist } from "@middlewares/persist";
import type { StoreApi, UseBoundStore } from "zustand";
import { create } from "zustand";

type User = {
  id: string;
  name: string;
  roles: string[];
};

type AuthStore = {
  user: User | null;
  isAuthenticated: boolean;
  login: (user: User) => void;
  logout: () => void;
};

const KEY = {
  NAME: "UserStore",
  STORAGE: "auth-storage",
};

const createAuthStore = (set: StoreApi<AuthStore>["setState"]): AuthStore => ({
  isAuthenticated: false,
  login: (user) => set({ isAuthenticated: true, user }),
  logout: () => set({ isAuthenticated: false, user: null }),
  user: null,
});

export const useUserStore: UseBoundStore<StoreApi<AuthStore>> = create<AuthStore>()(
  withDevtools(
    withPersist(createAuthStore, {
      name: KEY.STORAGE,
      partialize: ({ user, isAuthenticated }) => ({ isAuthenticated, user }),
    }),
    { name: KEY.NAME },
  ),
);
