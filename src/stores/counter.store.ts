import type { StoreA<PERSON>, UseBoundStore } from "zustand";
import { create } from "zustand";

type CounterStore = {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
};

export const useCounterStore: UseBoundStore<StoreApi<CounterStore>> = create<CounterStore>(
  (set) => ({
    count: 0,
    decrement: () =>
      set((state) => ({
        count: state.count - 1,
      })),
    increment: () =>
      set((state) => ({
        count: state.count + 1,
      })),
    reset: () => set({ count: 0 }),
  }),
);
