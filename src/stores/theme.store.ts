import { THEME } from "@enums/theme";
import { withDevtools } from "@middlewares/devtools";
import { withPersist } from "@middlewares/persist";
import type { StoreApi, UseBoundStore } from "zustand";
import { create } from "zustand";

type ThemeState = {
  theme: THEME;
  toggleTheme: () => void;
};

const KEY = {
  NAME: "ThemeStore",
  STORAGE: "theme-storage",
};

const DATA_THEME = "data-theme";

const getInitialTheme = (): THEME => {
  if (typeof document === "undefined") {
    return THEME.LIGHT;
  }

  const current = document.documentElement.getAttribute(DATA_THEME) as THEME | null;
  const theme = current === THEME.DARK ? THEME.DARK : THEME.LIGHT;

  document.documentElement.setAttribute(DATA_THEME, theme);
  return theme;
};

const createThemeStore = (set: StoreApi<ThemeState>["setState"]): ThemeState => ({
  theme: getInitialTheme(),
  toggleTheme: () =>
    set((state) => {
      const newTheme = state.theme === THEME.DARK ? THEME.LIGHT : THEME.DARK;
      document.documentElement.setAttribute(DATA_THEME, newTheme);
      return { theme: newTheme };
    }),
});

export const useThemeStore: UseBoundStore<StoreApi<ThemeState>> = create<ThemeState>()(
  withDevtools(
    withPersist(createThemeStore, {
      name: KEY.STORAGE,
      partialize: ({ theme }) => ({ theme }),
    }),
    { name: KEY.NAME },
  ),
);
