import type { StateCreator } from "zustand";

export const logger =
  <T>(config: StateCreator<T>): StateCreator<T> =>
  (set, get, api) =>
    config(
      (partial, replace) => {
        // biome-ignore lint/suspicious/noConsole: Logging for debugging purposes
        console.log("[Zustand] State update:", partial);
        if (replace) {
          set(partial as T, true);
        } else {
          set(partial as Partial<T>, false);
        }
      },
      get,
      api,
    );
