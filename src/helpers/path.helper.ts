/**
 * Removes the trailing slash from a path if present,
 * except for the root path ("/").
 *
 * @param path - The input path string.
 * @returns The path without a trailing slash.
 *
 * @example
 * PathHelper.removeTrailingSlash("/about/") // "/about"
 * PathHelper.removeTrailingSlash("/")       // "/"
 */
export const removeTrailingSlash = (path: string): string => {
  return path.length > 1 && path.endsWith("/") ? path.slice(0, -1) : path;
};
