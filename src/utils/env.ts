import { ENV } from "../constants/env";
import { isTruthyBoolean } from "../helpers/boolean.helper";

export const isProd = (mode: string) => mode === ENV.PRODUCTION;
export const isDev = (mode: string) => mode === ENV.DEVELOPMENT;

export const parseEnv = (raw: Record<string, string>): ImportMetaEnv => {
  const {
    VITE_SENTRY_ORG,
    VITE_SENTRY_PROJECT,
    VITE_SENTRY_AUTH_TOKEN,
    VITE_SENTRY_TELEMETRY,
    VITE_SENTRY_IS_USE,
    VITE_SENTRY_DSN,
    NODE_ENV,
    BASE_URL,
  } = raw;

  return {
    BASE_URL,
    DEV: isDev(NODE_ENV),
    MODE: NODE_ENV,
    PROD: isProd(NODE_ENV),
    SSR: false,
    VITE_SENTRY_AUTH_TOKEN,
    VITE_SENTRY_DSN,
    VITE_SENTRY_IS_USE: isTruthyBoolean(VITE_SENTRY_IS_USE),
    VITE_SENTRY_ORG,
    VITE_SENTRY_PROJECT,
    VITE_SENTRY_TELEMETRY: isTruthyBoolean(VITE_SENTRY_TELEMETRY),
  };
};
