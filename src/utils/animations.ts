import { cn } from "@utils/cn";
import { useState } from "react";

// Animation presets for common show/hide patterns
export const animations = {
  backdrop: {
    base: "transition-opacity duration-300 ease-in-out",
    hide: "opacity-0",
    show: "opacity-100",
  },
  // Fade animations
  fade: {
    base: "transition-opacity duration-300 ease-in-out",
    hide: "opacity-0",
    show: "opacity-100",
  },

  fadeScale: {
    base: "transition-all duration-200 ease-out",
    hide: "scale-95 opacity-0",
    show: "scale-100 opacity-100",
  },

  fadeSlideDown: {
    base: "transition-all duration-200 ease-out",
    hide: "translate-y-2 opacity-0",
    show: "translate-y-0 opacity-100",
  },

  // Combined animations
  fadeSlideLeft: {
    base: "transition-all duration-300 ease-in-out",
    hide: "-translate-x-4 opacity-0",
    show: "translate-x-0 opacity-100",
  },

  fadeSlideRight: {
    base: "transition-all duration-300 ease-in-out",
    hide: "translate-x-4 opacity-0",
    show: "translate-x-0 opacity-100",
  },

  fadeSlideUp: {
    base: "transition-all duration-200 ease-out",
    hide: "-translate-y-2 opacity-0",
    show: "translate-y-0 opacity-100",
  },

  // Height animations (for accordions)
  height: {
    base: "overflow-hidden transition-all duration-500 ease-in-out",
    hide: "max-h-0 opacity-0",
    show: "max-h-96 opacity-100",
  },

  // Modal/overlay animations
  modal: {
    base: "transition-all duration-200 ease-out",
    hide: "scale-95 opacity-0",
    show: "scale-100 opacity-100",
  },

  // Scale animations
  scale: {
    base: "transition-transform duration-300 ease-in-out origin-center",
    hide: "scale-0",
    show: "scale-100",
  },

  scaleUp: {
    base: "transition-transform duration-200 ease-out",
    hide: "scale-95",
    show: "scale-100",
  },

  // Sidebar-style animation
  sidebar: {
    base: "transition-all duration-300 ease-in-out",
    hide: "max-w-0 translate-x-4 scale-95 overflow-hidden opacity-0",
    show: "max-w-none translate-x-0 scale-100 opacity-100",
  },

  slideDown: {
    base: "transition-transform duration-500 ease-in-out",
    hide: "translate-y-full",
    show: "translate-y-0",
  },

  // Slide animations
  slideLeft: {
    base: "transition-transform duration-500 ease-in-out",
    hide: "-translate-x-full",
    show: "translate-x-0",
  },

  slideRight: {
    base: "transition-transform duration-500 ease-in-out",
    hide: "translate-x-full",
    show: "translate-x-0",
  },

  slideUp: {
    base: "transition-transform duration-500 ease-in-out",
    hide: "-translate-y-full",
    show: "translate-y-0",
  },

  // Width animations (for sidebars)
  width: {
    base: "overflow-hidden transition-all duration-500 ease-in-out",
    hide: "max-w-0 opacity-0",
    show: "max-w-none opacity-100",
  },
} as const;

// Animation utility function
export const getAnimationClasses = (
  animationType: keyof typeof animations,
  isVisible: boolean,
  additionalClasses?: string,
) => {
  const animation = animations[animationType];
  return cn(animation.base, isVisible ? animation.show : animation.hide, additionalClasses);
};

// Hook for managing animation states
export const useAnimation = (initialState = false) => {
  const [isVisible, setIsVisible] = useState(initialState);

  const toggle = () => setIsVisible(!isVisible);
  const show = () => setIsVisible(true);
  const hide = () => setIsVisible(false);

  return {
    getClasses: (animationType: keyof typeof animations, additionalClasses?: string) =>
      getAnimationClasses(animationType, isVisible, additionalClasses),
    hide,
    isVisible,
    show,
    toggle,
  };
};

// Stagger animation utility for lists
export const getStaggerDelay = (index: number, baseDelay = 50) => ({
  transitionDelay: `${index * baseDelay}ms`,
});

// Animation variants for different speeds
export const speeds = {
  fast: "duration-150",
  normal: "duration-300",
  slow: "duration-500",
  slower: "duration-700",
} as const;

// Easing variants
export const easings = {
  bounce: "ease-bounce",
  in: "ease-in",
  inOut: "ease-in-out",
  linear: "ease-linear",
  out: "ease-out",
} as const;
