import { isAfter, isBefore, isEqual, startOfDay } from "date-fns";

export const formatDate = (date: Date) =>
  new Intl.DateTimeFormat("th-TH", {
    day: "2-digit",
    month: "2-digit",
    year: "2-digit",
  }).format(date);

/**
 * Checks if target is strictly in the past compared to now (by time).
 * Returns true if target < now.
 */
export function isPast(target: Date) {
  const now = new Date();
  return isBefore(target, now);
}

/**
 * Checks if target is strictly in the future compared to now (by time).
 * Returns true if target > now.
 */
export function isFuture(target: Date) {
  const now = new Date();
  return isAfter(target, now);
}

/**
 * Checks if target is now (same millisecond).
 * Useful if you need three-way classification.
 */
export function isNow(target: Date) {
  const now = new Date();
  return isEqual(target, now);
}

/**
 * Day-level comparison: ignores time-of-day.
 * Returns:
 * -1 if target day is before today
 *  0 if target day is today
 *  1 if target day is after today
 */
export function compareByDay(target: Date) {
  const todayStart = startOfDay(new Date());
  const targetStart = startOfDay(target);

  if (isBefore(targetStart, todayStart)) {
    return -1;
  }
  if (isAfter(targetStart, todayStart)) {
    return 1;
  }
  return 0;
}

/**
 * Convenience wrappers for day-level checks (ignore time):
 */
export function isPastDay(target: Date) {
  return compareByDay(target) === -1;
}

export function isToday(target: Date) {
  return compareByDay(target) === 0;
}

export function isFutureDay(target: Date) {
  return compareByDay(target) === 1;
}
