import type { LeadProps } from "./interface";

const today = new Date();

export const leads: LeadProps[] = [
  {
    followUpDate: today,
    leadStatus: "draft",
    name: "<PERSON>",
    pipelineStage: "contacted",
    qualification: "hot",
    servicesInterest: "botox",
    sourceChannel: "line",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "active",
    name: "John Donut",
    pipelineStage: "interested",
    qualification: "hot",
    servicesInterest: "hifu",
    sourceChannel: "instagram",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "completed",
    name: "<PERSON>-Re-Mi",
    pipelineStage: "pending",
    qualification: "warm",
    servicesInterest: "thermage",
    sourceChannel: "tiktok",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: true, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "suspended",
    name: "<PERSON>mon",
    pipelineStage: "not_interested",
    qualification: "cold",
    servicesInterest: "juvelook",
    sourceChannel: "facebook",
    tasks: [
      { completed: false, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "draft",
    name: "Alice",
    pipelineStage: "contacted",
    qualification: "hot",
    servicesInterest: "botox",
    sourceChannel: "line",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "active",
    name: "Bobby",
    pipelineStage: "interested",
    qualification: "hot",
    servicesInterest: "hifu",
    sourceChannel: "instagram",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "completed",
    name: "Anderson",
    pipelineStage: "pending",
    qualification: "warm",
    servicesInterest: "thermage",
    sourceChannel: "tiktok",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: true, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "suspended",
    name: "Nobita",
    pipelineStage: "not_interested",
    qualification: "cold",
    servicesInterest: "juvelook",
    sourceChannel: "facebook",
    tasks: [
      { completed: false, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "draft",
    name: "Jacky",
    pipelineStage: "contacted",
    qualification: "hot",
    servicesInterest: "botox",
    sourceChannel: "line",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "active",
    name: "Toby",
    pipelineStage: "interested",
    qualification: "hot",
    servicesInterest: "hifu",
    sourceChannel: "instagram",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "completed",
    name: "Romeo",
    pipelineStage: "pending",
    qualification: "warm",
    servicesInterest: "thermage",
    sourceChannel: "tiktok",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: true, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "suspended",
    name: "Jane",
    pipelineStage: "not_interested",
    qualification: "cold",
    servicesInterest: "juvelook",
    sourceChannel: "facebook",
    tasks: [
      { completed: false, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "draft",
    name: "Jojo",
    pipelineStage: "contacted",
    qualification: "hot",
    servicesInterest: "botox",
    sourceChannel: "line",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "active",
    name: "J Jetarin",
    pipelineStage: "interested",
    qualification: "hot",
    servicesInterest: "hifu",
    sourceChannel: "instagram",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "completed",
    name: "Jimmy",
    pipelineStage: "pending",
    qualification: "warm",
    servicesInterest: "thermage",
    sourceChannel: "tiktok",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: true, task: "Follow up" },
    ],
  },
  {
    followUpDate: today,
    leadStatus: "suspended",
    name: "Austin",
    pipelineStage: "not_interested",
    qualification: "cold",
    servicesInterest: "juvelook",
    sourceChannel: "facebook",
    tasks: [
      { completed: false, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
  },
];
