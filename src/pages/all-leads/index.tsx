import {
  ChannelBadge,
  type ChanneType,
  LEAD_STATUS_OPTIONS,
  LeadStatusBadge,
  type LeadStatusType,
  PipelineStageBadge,
  type PipelineStageType,
  QualificationBadge,
  type QualificationType,
  ServiceBadge,
  type ServiceType,
} from "@components/badge";
import { Container, Progress } from "@components/common";
import { filterLeads } from "@components/filterDrawer/filterLead";
import { FilterToggle } from "@components/filterDrawer/filterToogle";
import { RouthPath } from "@enums/route-path";
import { CaretRightIcon } from "@phosphor-icons/react";
import { useNavigate } from "@tanstack/react-router";
import { cn } from "@utils/cn";
import { formatDate } from "@utils/date";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import type { LeadProps } from "./interface";
import { leads } from "./mock";

interface PageTitleBarProps {
  leadCount: number;
  onApplyFilters: (filters: SelectedFilters) => void;
}

import type { SelectedFilters } from "@components/filterDrawer/interface";

const PageTitleBar = ({ leadCount, onApplyFilters }: PageTitleBarProps) => {
  const { t } = useTranslation();
  return (
    <div className="my-4 flex w-full justify-between">
      <div className="flex h-fit items-center gap-2">
        <h3>{t("allLeads.allLead")}</h3>
        <div className="size-6 place-content-center rounded-full bg-secondary">
          <h6 className="place-self-center text-white">{leadCount}</h6>
        </div>
      </div>
      <div>
        <FilterToggle onApply={onApplyFilters} tasksStatusOptions={LEAD_STATUS_OPTIONS} />
      </div>
    </div>
  );
};

const TabalTitleBar = () => {
  const { t } = useTranslation();
  return (
    <div
      className={cn(
        "grid grid-cols-[2%_10%_10%_16%_1fr_10%_10%_10%_10%_4%]",
        "place-items-center rounded-lg bg-primary p-4 text-white",
      )}
    >
      <div />
      <h6>{t("allLeads.qualification")}</h6>
      <h6>{t("allLeads.sourceChannel")}</h6>
      <h6>{t("allLeads.name")}</h6>
      <h6>{t("allLeads.servicesInterest")}</h6>
      <h6>{t("allLeads.pipelineStage")}</h6>
      <h6>{t("allLeads.followUpDate")}</h6>
      <h6>{t("allLeads.tasks")}</h6>
      <h6>{t("allLeads.leadStatus")}</h6>
      <div />
    </div>
  );
};

const LeadRow = ({ lead, index }: { lead: LeadProps; index: number }) => {
  const navigate = useNavigate();
  return (
    <button
      type="button"
      className={cn(
        "grid h-12 w-full cursor-pointer grid-cols-[3%_10%_10%_15%_1fr_10%_10%_10%_10%_5%]",
        "place-items-center border-base-300 border-b",
        "hover:bg-success-content/50",
      )}
      onClick={() => navigate({ to: `${RouthPath.PROFILE}/${lead.name}` })}
    >
      <h6>{index + 1}.</h6>
      <QualificationBadge type={lead.qualification as QualificationType} />
      <ChannelBadge type={lead.sourceChannel as ChanneType} />
      <p className="text-body-sm">{lead.name}</p>
      <ServiceBadge type={lead.servicesInterest as ServiceType} />
      <PipelineStageBadge type={lead.pipelineStage as PipelineStageType} />
      <p className="text-body-sm">{formatDate(lead.followUpDate)}</p>
      <Progress tasks={lead.tasks} />
      <LeadStatusBadge type={lead.leadStatus as LeadStatusType} />
      <CaretRightIcon size={24} weight="bold" />
    </button>
  );
};

export function AllLeads() {
  const data = [...leads];

  const [filteredData, setFilteredData] = useState(data);

  const handleApplyFilters = (filters: SelectedFilters) => {
    const result = filterLeads(data, filters, {
      assignees: "name",
      badges: "pipelineStage",
      qualifications: "qualification",
      services: "servicesInterest",
      sourceChannels: "sourceChannel",
      tasksStatuses: (lead) => lead.leadStatus,
    });

    setFilteredData(result);
  };

  return (
    <Container>
      <PageTitleBar leadCount={filteredData.length} onApplyFilters={handleApplyFilters} />
      <div className="flex-1 overflow-auto border-container">
        <TabalTitleBar />
        <div className="overflow-auto">
          {filteredData.map((lead, i) => (
            <LeadRow key={lead.name} lead={lead} index={i} />
          ))}
        </div>
      </div>
    </Container>
  );
}
