import {
  ChannelBadge,
  LeadStatusBadge,
  PipelineStageBadge,
  QualificationBadge,
  ServiceBadge,
  type ServiceType,
  TaskStatusBadge,
} from "@components/badge";
import { Avatar, Divider, Progress } from "@components/common";
import { EditLeadToggle } from "@components/leadDrawer/editLeadToggle";
import { HashIcon } from "@phosphor-icons/react";
import { ArrowsOutIcon } from "@phosphor-icons/react/dist/ssr";
import { cn } from "@utils/cn";
import type { ReactNode } from "react";
import { useTranslation } from "react-i18next";

const Stat = ({
  label,
  value,
  className,
}: {
  label: string;
  value: string | ReactNode;
  className?: string;
}) => (
  <div className={cn("flex flex-col", className)}>
    <p className="text-xs">{label}</p>
    <p className="text-info text-xs">{value}</p>
  </div>
);

const LeadDetail = () => {
  const { t } = useTranslation();
  return (
    <div className="!h-fit flex flex-col gap-4 border-container shadow-lg">
      <div className="flex flex-col gap-2">
        <div className="relative flex items-start gap-4 px-4">
          <Avatar
            size="xl"
            image="https://img.daisyui.com/images/profile/demo/<EMAIL>"
          />
          <div className="flex h-full flex-1 flex-col gap-2">
            <h4 className="line-clamp-1">Mrs. Aims Clinic</h4>

            <div className="flex justify-between">
              <Stat label={t("leadProfile.registerDate")} value="01 Jan 2023" />
              <Stat label={t("leadProfile.lastEdited")} value="31 Jan 2023" />
              <Stat
                label={t("leadProfile.currentStatus")}
                value=<LeadStatusBadge type="active" size="sm" />
              />
            </div>
          </div>
          <div className="absolute top-0 right-0">
            <EditLeadToggle />
          </div>
        </div>
        <div className="flex w-fit items-center gap-0.5">
          <HashIcon size={16} className="text-info" />
          <PipelineStageBadge type="interested" size="sm" />
        </div>
      </div>
      <div className="grid grid-cols-[1fr_2fr] gap-y-2">
        <p className="mt-0.5 text-label-xs">{t("leadProfile.qualification")}</p>
        <QualificationBadge type="warm" size="sm" />
        <Divider className="col-span-2" />
        <p className="mt-0.5 text-label-xs">{t("leadProfile.sourceChannel")}</p>
        <ChannelBadge type="line" size="sm" />
        <Divider className="col-span-2" />
        <p className="mt-0.5 text-label-xs">{t("leadProfile.servicesInterest")}</p>
        <div className="flex flex-row flex-wrap gap-2">
          {["hifu", "botox", "juvelook", "thermage"].map((service) => (
            <ServiceBadge key={service} type={service as ServiceType} />
          ))}
        </div>
        <Divider className="col-span-2" />
        <p className="mt-0.5 text-label-xs">{t("leadProfile.contactInfo")}</p>
        <p className="text-body-xs text-info">098-123-4567 , @linenarak</p>
      </div>
      <div className="!h-36 relative mt-0.5 gap-1 border-container">
        <TaskStatusBadge
          type="completed"
          label="02-02-68"
          className="place-self-center"
          size="xs"
        />
        <p className="text-body-xs text-info">098-123-4567 , @linenarak</p>
        <ArrowsOutIcon size={20} className="absolute right-2 text-base-300" />
      </div>
    </div>
  );
};

const LeadStatistics = () => {
  const { t } = useTranslation();
  return (
    <div className="!min-h-36 !h-fit !p-4 relative mt-4 gap-6 border-container shadow-lg">
      <div className="flex items-center gap-4">
        <Stat label="" value={<Progress />} />
        <h6>{t("leadProfile.statistics")}</h6>
      </div>

      <div className="grid grid-cols-2 gap-y-6">
        <Stat label={t("leadProfile.followUpDate")} value="31 Jan 2023" />
        <Stat label={t("leadProfile.totalDayToNextFollowUp")} value="7" />
        <Stat label={t("leadProfile.lastFollowUpDate")} value="12 Jan 2023" />
        <Stat label={t("leadProfile.totalDayFromLastFollowUp")} value="7" />
        <Stat label={t("leadProfile.startDate")} value="1 Jan 2023" />
        <Stat label={t("leadProfile.totalDayFromStartDate")} value="20" />
      </div>
    </div>
  );
};

export const LeadProfile = () => {
  return (
    <div>
      <LeadDetail />
      <LeadStatistics />
    </div>
  );
};
