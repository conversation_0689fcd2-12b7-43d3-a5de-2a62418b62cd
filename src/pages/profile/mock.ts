import { addDays } from "date-fns";
import type { TaskProps } from "./interface";

const today = new Date();

export const tasks: TaskProps[] = [
  {
    assignee: "",
    createdAt: addDays(today, -7),
    detail:
      "แตงโม แตงโม แตงโม แตงโมลูกโตโตรสหวาน ใครรับประทาน ถูกอกถูกใจ แตงโมจินตหรามาใหม่ แตงโมจินตหรามาใหม่ ผ่าดูข้างใน เนื้อแดงจ่ายหว่าย วันนี้มาขายราคาไม่แพง วันนี้มาขายราคาไม่แพง",
    duedate: addDays(today, 2),
    isCompleted: false,
    source: "https://www.google.com/?client=safari",
    title: "ส่งฮอลคูลไป",
  },
  {
    assignee: "",
    createdAt: addDays(today, -7),
    detail:
      "แตงโม แตงโม แตงโม แตงโมลูกโตโตรสหวาน ใครรับประทาน ถูกอกถูกใจ แตงโมจินตหรามาใหม่ แตงโมจินตหรามาใหม่ ผ่าดูข้างใน เนื้อแดงจ่ายหว่าย วันนี้มาขายราคาไม่แพง วันนี้มาขายราคาไม่แพง",
    duedate: today,
    isCompleted: false,
    source: "https://www.google.com/?client=safari",
    title: "ขายเครื่องกรองน้ำ",
  },
  {
    assignee: "",
    createdAt: addDays(today, -7),
    detail:
      "แตงโม แตงโม แตงโม แตงโมลูกโตโตรสหวาน ใครรับประทาน ถูกอกถูกใจ แตงโมจินตหรามาใหม่ แตงโมจินตหรามาใหม่ ผ่าดูข้างใน เนื้อแดงจ่ายหว่าย วันนี้มาขายราคาไม่แพง วันนี้มาขายราคาไม่แพง",
    duedate: today,
    isCompleted: true,
    source: "https://www.google.com/?client=safari",
    title: "ขายเครื่องกรองน้ำ",
  },
  {
    assignee: "",
    createdAt: addDays(today, -7),
    detail:
      "แตงโม แตงโม แตงโม แตงโมโตโตรสหวาน ใครอกใจ แตงโมจินตหรามาใหม่ แตงโมจินตหรามาใหม่ ผ่าข้างใน เนื้อแดงจ่ายหว่ายมาขายราคาไม่แพงมาขายราคาไม่แพง",
    duedate: addDays(today, -1),
    isCompleted: false,
    source: "https://www.google.com/?client=safari",
    title: "โทรสแปม",
  },
  {
    assignee: "",
    createdAt: addDays(today, -7),
    detail:
      "แตงโม แตงโม แตงโม แตงโมโตโตรสหวาน ใครอกใจ แตงโมจินตหรามาใหม่ แตงโมจินตหรามาใหม่ ผ่าข้างใน เนื้อแดงจ่ายหว่ายมาขายราคาไม่แพงมาขายราคาไม่แพง",
    duedate: addDays(today, -2),
    isCompleted: true,
    source: "https://www.google.com/?client=safari",
    title: "ส่ง sms ทวงหนี้",
  },
];
