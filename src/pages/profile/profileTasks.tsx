import { AddTaskF<PERSON>, CreateTask } from "@components/addTask";
import { TaskStatusBadge, type TaskStatusType } from "@components/badge";
import { <PERSON><PERSON>, Button, Container, Modal } from "@components/common";
import {
  CheckFatIcon,
  DotsThreeOutlineVerticalIcon,
  PlusSquareIcon,
  XIcon,
} from "@phosphor-icons/react";
import { TicketIcon } from "@phosphor-icons/react/dist/ssr";
import { cn } from "@utils/cn";
import { formatDate, isFuture, isPastDay, isToday } from "@utils/date";
import { useState } from "react";
import type { TaskProps } from "./interface";
import { tasks } from "./mock";

const CARD_STYLE = {
  completed: "text-neutral-content bg-base-200/30 border border-base-300",
  duedate:
    "bg-success-content border-primary/80 border-dashed shadow-lg ring-1 ring-offset-3 ring-primary",
  overdue: "text-secondary bg-secondary-content/20 border-dashed border-secondary-content",
  upcoming: "text-info/80 bg-sky-50/50 border border-sky-50",
};

const taskStatus = (
  isCompleted: boolean,
  dueDate: Date,
): { label: string; type: TaskStatusType } => {
  if (isCompleted) {
    return { label: "Completed", type: "completed" };
  }
  if (isToday(dueDate) && !isCompleted) {
    return { label: "Today", type: "duedate" };
  }
  if (isPastDay(dueDate)) {
    return { label: "Overdue", type: "overdue" };
  }
  return { label: "Upcoming", type: "upcoming" };
};

const TaskCard = ({ task }: { task: TaskProps }) => {
  const [isCompleted, setIsCompleted] = useState(false);
  const { label, type } = taskStatus(task.isCompleted, task.duedate);

  return (
    <Container
      className={cn("!h-fit my-2 gap-4 rounded-lg border p-2", CARD_STYLE[type], {
        "text-base-content": isToday,
      })}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h4>{task.title}</h4>
          <TaskStatusBadge type={type} label={label} className="rounded-lg bg-white" />
          <p className="text-label-xs">{formatDate(task.duedate)}</p>
        </div>
        <div className="flex items-center gap-2">
          {isToday(task.duedate) && (
            <Button
              disabled={isCompleted || type === "completed"}
              variant="icon"
              onClick={() => setIsCompleted(!isCompleted)}
              className="size-6 bg-white ring-2"
            >
              {(isCompleted || type === "completed") && (
                <CheckFatIcon
                  size={18}
                  weight="fill"
                  className={cn({ "text-primary": isCompleted })}
                />
              )}
            </Button>
          )}
          <DotsThreeOutlineVerticalIcon size={16} weight="fill" />
        </div>
      </div>

      <div className="flow-root" />

      <div className="flex items-start gap-2">
        <p className="text-label-sm">Detail:</p>
        <p className="whitespace-pre-wrap text-xs">{task.detail}</p>
      </div>

      <div className="flow-root" />

      <div className="flex items-start gap-2">
        <p className="text-label-sm">Source:</p>
        <a href={task.source} className="text-blue-400 text-body-xs underline">
          {task.source}
        </a>
      </div>

      <div className="flow-root" />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <p className="text-label-xs">Assignee :</p>
          <Avatar image={task.assignee} />
        </div>
        <p className="text-label-xs">Create at : {formatDate(task.createdAt)}</p>
      </div>
    </Container>
  );
};

const CreatetaskButton = () => {
  return (
    <div className="flex h-full flex-col items-center justify-center gap-2">
      <Button
        variant="outline"
        className="!rounded-full aspect-square h-32 border-neutral hover:border-base-300 hover:bg-neutral-100"
        onClick={() =>
          (document.getElementById("create_task_modal") as HTMLDialogElement)?.showModal()
        }
      >
        <div className="flex flex-col">
          <TicketIcon size={60} type="button" className="cursor-pointer text-neutral" />
          <h6 className="text-neutral">Add Task</h6>
        </div>
      </Button>
    </div>
  );
};

export const ProfileTasks = () => {
  const [isAddTasks, setIsAddTasks] = useState(false);

  return (
    <div className="flex-1 overflow-auto px-4 pt-4">
      {isAddTasks ? (
        tasks.map((task, index) => (
          <div key={`${task.title}-${index}`} className="flex flex-col items-center gap-2">
            {(isToday(task.duedate) || isFuture(task.duedate)) && !task.isCompleted && (
              <>
                <PlusSquareIcon
                  size={24}
                  onClick={() =>
                    (document.getElementById("add_task_modal") as HTMLDialogElement)?.showModal()
                  }
                  type="button"
                  className="cursor-pointer"
                />
                <Modal id="add_task_modal">
                  <form method="dialog" className="place-self-end text-neutral">
                    <button type="button" className="cursor-pointer">
                      <XIcon size={24} />
                    </button>
                  </form>
                  <AddTaskForm id="add_task_modal" />
                </Modal>
              </>
            )}
            <TaskCard task={task} />
          </div>
        ))
      ) : (
        <>
          <CreatetaskButton />
          <Modal id="create_task_modal">
            <CreateTask createTask={() => setIsAddTasks(true)} />
          </Modal>
        </>
      )}
    </div>
  );
};
