import { Avatar, Container } from "@components/common";
import { Button } from "@components/common/button";
import { CaretLeftIcon } from "@phosphor-icons/react";
import { useRouter } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { LeadProfile } from "./leadProfile";
import { ProfileTasks } from "./profileTasks";

const TaskDetail = () => {
  const { t } = useTranslation();
  const router = useRouter();
  return (
    <Container className="flex-1">
      <div className="!pb-0 mb-1 gap-2 border-container shadow-lg">
        <div className="flex items-center justify-between">
          <Button variant="ghost">
            <CaretLeftIcon size={24} weight="bold" onClick={() => router.history.back()} />
          </Button>
          <div className="mr-2 flex items-center gap-8">
            <Avatar />
            <Button variant="secondary" className="h-fit py-1">
              {t("leadProfile.newTask")}
            </Button>
          </div>
        </div>
        <ProfileTasks />
      </div>
    </Container>
  );
};

export function Profile() {
  return (
    <>
      <TaskDetail />
      <Container className="w-1/3">
        <LeadProfile />
      </Container>
    </>
  );
}
