import type { LeadProps } from "./interface";

const today = new Date();

export const leads: LeadProps[] = [
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: "089-123-4567",
    followUpDate: today,
    isCompleted: true,
    name: "<PERSON>",
    note: "คืนวันพรุ่งนี้แล้ว! 7 กันยายน ชวนดูปรากฏการณ์จันทรุปราคาเต็มดวง เห็นดวงจันทร์ปรากฏเป็นสีแดงส้ม อยู่ใต้เงาโลกนานกว่า 1 ชั่วโมง 22 นาที",
    pipelineStage: "contacted",
    qualification: "hot",
    servicesInterest: "botox",
    sourceChannel: "line",
    taskDescription:
      "สวัสดีค่ะคุณผู้หญิง 🙏 แอดมินขออนุญาตสอบถามค่ะ 😊\nไม่ทราบว่าคุณผู้หญิงสนใจโปรโมชันใดเป็นพิเศษไหมคะ 😊\nหากต้องการข้อมูลส่วนใดเพิ่มเติม สามารถแจ้งแอดมินได้เลยนะคะ แอดมินพร้อมดูแลค่ะ 🙏😊",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Call Back",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: "089-123-4567",
    followUpDate: today,
    isCompleted: false,
    name: "John Don<PERSON>",
    note: null,
    pipelineStage: "interested",
    qualification: "hot",
    servicesInterest: "hifu",
    sourceChannel: "instagram",
    taskDescription: "พรรคใหญ่กว่าคน\nประชนใหญ่กว่าพรรค",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Schedule Appointment",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: null,
    followUpDate: today,
    isCompleted: false,
    name: "John Do-Re-Mi",
    note: null,
    pipelineStage: "pending",
    qualification: "warm",
    servicesInterest: "thermage",
    sourceChannel: "tiktok",
    taskDescription: "สวัสดีค่ะคุณพรี่ 🙏 น้อนขออนุญาตสอบถามค่ะ 😊\nไม่ทราบว่าคุณพรี่สนใจโปรโมชันใดเป็นพิเศษไหมคะ",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Call Back",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: null,
    followUpDate: today,
    isCompleted: false,
    name: "John Doreamon",
    note: null,
    pipelineStage: "not_interested",
    qualification: "cold",
    servicesInterest: "juvelook",
    sourceChannel: "facebook",
    taskDescription: "Schedule appointment for next week",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Call Back",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: "089-123-4567",
    followUpDate: today,
    isCompleted: true,
    name: "John Cena",
    note: "คืนวันพรุ่งนี้แล้ว! 7 กันยายน ชวนดูปรากฏการณ์จันทรุปราคาเต็มดวง เห็นดวงจันทร์ปรากฏเป็นสีแดงส้ม อยู่ใต้เงาโลกนานกว่า 1 ชั่วโมง 22 นาที",
    pipelineStage: "contacted",
    qualification: "hot",
    servicesInterest: "botox",
    sourceChannel: "line",
    taskDescription:
      "สวัสดีค่ะคุณผู้หญิง 🙏 แอดมินขออนุญาตสอบถามค่ะ 😊\nไม่ทราบว่าคุณผู้หญิงสนใจโปรโมชันใดเป็นพิเศษไหมคะ 😊\nหากต้องการข้อมูลส่วนใดเพิ่มเติม สามารถแจ้งแอดมินได้เลยนะคะ แอดมินพร้อมดูแลค่ะ 🙏😊",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Call Back",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: "089-123-4567",
    followUpDate: today,
    isCompleted: false,
    name: "John Tuna",
    note: null,
    pipelineStage: "interested",
    qualification: "hot",
    servicesInterest: "hifu",
    sourceChannel: "instagram",
    taskDescription: "พรรคใหญ่กว่าคน\nประชนใหญ่กว่าพรรค",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Schedule Appointment",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: null,
    followUpDate: today,
    isCompleted: false,
    name: "John two three four",
    note: null,
    pipelineStage: "pending",
    qualification: "warm",
    servicesInterest: "thermage",
    sourceChannel: "tiktok",
    taskDescription: "สวัสดีค่ะคุณพรี่ 🙏 น้อนขออนุญาตสอบถามค่ะ 😊\nไม่ทราบว่าคุณพรี่สนใจโปรโมชันใดเป็นพิเศษไหมคะ",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Call Back",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: null,
    followUpDate: today,
    isCompleted: false,
    name: "John Dodo",
    note: null,
    pipelineStage: "not_interested",
    qualification: "cold",
    servicesInterest: "juvelook",
    sourceChannel: "facebook",
    taskDescription: "Schedule appointment for next week",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Call Back",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: "089-123-4567",
    followUpDate: today,
    isCompleted: true,
    name: "John Do Something",
    note: "คืนวันพรุ่งนี้แล้ว! 7 กันยายน ชวนดูปรากฏการณ์จันทรุปราคาเต็มดวง เห็นดวงจันทร์ปรากฏเป็นสีแดงส้ม อยู่ใต้เงาโลกนานกว่า 1 ชั่วโมง 22 นาที",
    pipelineStage: "contacted",
    qualification: "hot",
    servicesInterest: "botox",
    sourceChannel: "line",
    taskDescription:
      "สวัสดีค่ะคุณผู้หญิง 🙏 แอดมินขออนุญาตสอบถามค่ะ 😊\nไม่ทราบว่าคุณผู้หญิงสนใจโปรโมชันใดเป็นพิเศษไหมคะ 😊\nหากต้องการข้อมูลส่วนใดเพิ่มเติม สามารถแจ้งแอดมินได้เลยนะคะ แอดมินพร้อมดูแลค่ะ 🙏😊",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Call Back",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: "089-123-4567",
    followUpDate: today,
    isCompleted: false,
    name: "John wick",
    note: null,
    pipelineStage: "interested",
    qualification: "hot",
    servicesInterest: "hifu",
    sourceChannel: "instagram",
    taskDescription: "พรรคใหญ่กว่าคน\nประชนใหญ่กว่าพรรค",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Schedule Appointment",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: null,
    followUpDate: today,
    isCompleted: false,
    name: "John F",
    note: null,
    pipelineStage: "pending",
    qualification: "warm",
    servicesInterest: "thermage",
    sourceChannel: "tiktok",
    taskDescription: "สวัสดีค่ะคุณพรี่ 🙏 น้อนขออนุญาตสอบถามค่ะ 😊\nไม่ทราบว่าคุณพรี่สนใจโปรโมชันใดเป็นพิเศษไหมคะ",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Call Back",
  },
  {
    assignee: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    contactInfo: null,
    followUpDate: today,
    isCompleted: false,
    name: "John bravo",
    note: null,
    pipelineStage: "not_interested",
    qualification: "cold",
    servicesInterest: "juvelook",
    sourceChannel: "facebook",
    taskDescription: "Schedule appointment for next week",
    taskSource: "https://www.google.com/?client=safari",
    taskTitle: "Call Back",
  },
];
