/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_SENTRY_IS_USE: boolean;
  readonly VITE_SENTRY_ORG: string;
  readonly VITE_SENTRY_PROJECT: string;
  readonly VITE_SENTRY_AUTH_TOKEN: string;
  readonly VITE_SENTRY_TELEMETRY: boolean;
  readonly VITE_SENTRY_DSN: string;
  readonly MODE: "development" | "production" | "test";
}

declare interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare module "*.css" {
  const content: string;
  export default content;
}

declare module "*.scss" {
  const content: string;
  export default content;
}

declare module "*.sass" {
  const content: string;
  export default content;
}
