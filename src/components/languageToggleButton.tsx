import { LANGUAGE } from "@enums/language";
import { type JSX, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "./common";

export const LanguageToggleButton = (): JSX.Element => {
  const { i18n, t } = useTranslation();

  const nextLanguage = useMemo(
    () => (i18n.language === LANGUAGE.EN ? LANGUAGE.TH : LANGUAGE.EN),
    [i18n.language],
  );

  const handleToggleLanguage = useCallback(() => {
    i18n.changeLanguage(nextLanguage);
  }, [i18n, nextLanguage]);

  const label = t("common.lang");

  return (
    <div className="flex gap-2">
      <Button type="button" onClick={handleToggleLanguage} aria-label={label} variant="link">
        {label}
      </Button>
    </div>
  );
};
