import { PencilSimpleLineIcon } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import { AddLeadForm } from "./leadForm";

export const EditLeadToggle = () => {
  const { t } = useTranslation();

  return (
    <div className="drawer drawer-end">
      <input id="edit-drawer" type="checkbox" className="drawer-toggle" />
      <div className="drawer-content">
        <label htmlFor="edit-drawer">
          <PencilSimpleLineIcon size={20} className="cursor-pointer text-neutral" />
        </label>
      </div>
      <div className="drawer-side">
        <div className="drawer-overlay" />
        <div className="h-full w-1/2 rounded-l-2xl bg-accent-content p-6 text-base-content">
          <div className="flex h-full flex-1 flex-col gap-6 rounded-lg border bg-base-100 p-6">
            <h3>{t("leadProfile.editLead")}</h3>
            <div className="min-h-0 flex-1">
              <AddLeadForm id="edit-lead-form" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
