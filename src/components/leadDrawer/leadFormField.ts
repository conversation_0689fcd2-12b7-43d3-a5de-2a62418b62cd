import {
  PIPELINE_STAGE_OPTIONS,
  QUALIFICATION_OPTIONS,
  SERVICE_OPTIONS,
  SOURCE_CHANNEL_OPTIONS,
} from "@components/badge";
import type { FormField } from "../form/interface";

export const leadFormField = (t: (key: string) => string) => {
  const formFields: FormField[] = [
    {
      id: "name",
      label: t("addLead.name"),
      placeholder: t("addLead.name"),
      required: true,
      type: "input",
      variant: "transparent",
    },
    {
      id: "sourceChannel",
      label: t("addLead.sourceChannel"),
      options: SOURCE_CHANNEL_OPTIONS,
      placeholder: t("addLead.sourceChannel"),
      required: true,
      type: "select",
    },
    {
      id: "servicesInterest",
      label: t("addLead.servicesInterest"),
      options: SERVICE_OPTIONS,
      placeholder: t("addLead.servicesInterest"),
      type: "select",
    },
    {
      id: "qualification",
      label: t("addLead.qualification"),
      options: QUALIFICATION_OPTIONS,
      placeholder: t("addLead.qualification"),
      type: "select",
    },
    {
      id: "followUpStatus",
      label: t("addLead.pipelineStage"),
      options: PIPELINE_STAGE_OPTIONS,
      placeholder: t("addLead.pipelineStage"),
      type: "select",
    },
    {
      id: "contactInfo",
      label: t("addLead.contactInfo"),
      placeholder: t("addLead.contactInfo"),
      type: "input",
      variant: "transparent",
    },
  ];

  return formFields;
};
