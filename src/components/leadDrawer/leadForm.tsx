import { Button, Textarea } from "@components/common";
import { useForm } from "@tanstack/react-form";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { FormFields } from "../form/formFields";
import type { FormField, FormValues, FormWithField } from "../form/interface";
import { leadFormField } from "./leadFormField";

export const AddLeadForm = ({ id }: { id?: string }) => {
  const { t } = useTranslation();

  const isEditing = id !== undefined;

  const form = useForm({
    defaultValues: {
      contactInfo: "",
      leadStatus: "",
      name: "",
      note: "",
      pipelineStageStatus: "",
      qualification: "",
      servicesInterest: "",
      sourceChannel: "",
    },
    onSubmit: async ({ value }: { value: FormValues }) => {
      handleDrawerClose();
      alert(value);
    },
  });

  const renderField = useCallback(
    (field: FormField) => {
      return <FormFields key={field.id} form={form as FormWithField} field={field} column={3} />;
    },
    [form],
  );

  const noteField: FormField = {
    className: "w-full flex-1 resize-none",
    id: "note",
    label: t("addLead.note"),
    placeholder: t("addLead.note"),
    type: "textarea",
  };

  const renderNoteField = (field: FormField) => {
    return (
      <form.Field name="note">
        {(fieldApi) => (
          <div className="flex min-h-0 flex-1 flex-col gap-2">
            <label htmlFor={fieldApi.name} className="text-label-xs">
              {field.label}
            </label>
            <Textarea
              className={field.className}
              id={fieldApi.name}
              placeholder={field.placeholder}
              value={fieldApi.state.value}
              onChange={(e) => fieldApi.handleChange(e.target.value)}
            />
            {fieldApi.state.meta.errors.length > 0 && (
              <div className="text-error text-sm">{fieldApi.state.meta.errors[0]}</div>
            )}
          </div>
        )}
      </form.Field>
    );
  };

  const handleDrawerClose = useCallback(() => {
    const drawerToggle = document.getElementById(
      isEditing ? "edit-drawer" : "my-drawer",
    ) as HTMLInputElement;

    drawerToggle.checked = false;

    form.reset();
  }, [form, isEditing]);

  return (
    <form
      className="flex h-full flex-col gap-6 overflow-hidden p-1"
      onSubmit={(e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="grid grid-cols-2 gap-x-2 gap-y-6">
        {leadFormField(t).map((field) => renderField(field))}
      </div>
      {/* Note Field */}
      {renderNoteField(noteField)}

      {/* Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" className="w-28" type="button" onClick={handleDrawerClose}>
          {t("common.cancel")}
        </Button>

        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
          {([canSubmit, isSubmitting]) => (
            <Button className="w-28" type="submit" disabled={!canSubmit || isSubmitting}>
              {isSubmitting ? t("common.loading") : t("common.save")}
            </Button>
          )}
        </form.Subscribe>
      </div>
    </form>
  );
};
