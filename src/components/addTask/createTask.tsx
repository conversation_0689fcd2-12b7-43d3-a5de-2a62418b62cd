import type { TaskProps } from "@pages/profile/interface";
import { tasks } from "@pages/profile/mock";
import { CaretLeftIcon, FolderOpenIcon, PencilIcon, XIcon } from "@phosphor-icons/react";
import { NotebookIcon } from "@phosphor-icons/react/dist/ssr";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "../common";
import { AddTaskForm } from "./addTaskForm";

type Step = "initial" | "new" | "category" | "select";

const ModalLayout = ({
  title,
  step,
  setStep,
  closeModal,
  children,
}: {
  title: string;
  step: Step;
  setStep: (step: Step) => void;
  closeModal: () => void;
  children: React.ReactNode;
}) => (
  <div className="relative flex h-fit max-h-96 min-h-52 flex-col justify-between gap-6">
    <div className="flex items-start justify-between text-neutral">
      {step !== "initial" && <CaretLeftIcon size={24} onClick={() => setStep("initial")} />}
      <h4 className="w-full text-center text-base-content">{title}</h4>
      <form method="dialog">
        <button type="button" className="cursor-pointer" onClick={closeModal}>
          <XIcon size={24} />
        </button>
      </form>
    </div>
    <div className="flex h-fit flex-col gap-4 overflow-auto">{children}</div>
  </div>
);

const InitialStep = ({ setStep }: { setStep: (step: Step) => void }) => {
  const { t } = useTranslation();
  return (
    <>
      <Button
        variant="outline"
        className="h-16 w-full hover:bg-primary-content/10 hover:text-primary"
        type="button"
        onClick={() => setStep("new")}
      >
        <PencilIcon size={24} weight="fill" />
        {t("leadProfile.createNewTask")}
      </Button>
      <Button
        variant="primary"
        className="mb-4 h-16 w-full"
        type="button"
        onClick={() => setStep("category")}
      >
        <FolderOpenIcon size={24} />
        {t("leadProfile.selectFromTaskCategory")}
      </Button>
    </>
  );
};

const CategoryStep = ({ setStep }: { setStep: (step: Step) => void }) => (
  <>
    {["ตามตื้อแบบหวังผล", "ปลูกกุหลาบแดงไว้เพื่อเธอ", "รอนานๆก็อาจจะบั่นทอนหัวใจ"].map((text) => (
      <Button
        key={`create-button-${text}`}
        variant="outline"
        className="h-16 w-full hover:bg-primary-content/10 hover:text-primary"
        type="button"
        onClick={() => setStep("select")}
      >
        <div className="grid w-full grid-cols-[1fr_2fr] gap-4">
          <NotebookIcon size={24} className="place-self-end" />
          <p className="line-clamp-1 text-start">{text}</p>
        </div>
      </Button>
    ))}
  </>
);

const SelectTasks = ({ createTask }: { createTask: () => void }) => {
  // const tasksIndex = tasks.map((_, i) => i);

  // const [selected, setSelected] = useState<number[]>(tasksIndex);

  // const handleSelectTasks = () => {};

  return (
    <>
      {tasks.map((o: TaskProps) => (
        <Button
          key={o.title}
          variant="ghost"
          className="w-full text-primary hover:border-primary-content/30"
          type="button"
        >
          <div className="flex w-full justify-start">
            <label className="label gap-4 text-primary">
              <input type="checkbox" className="checkbox rounded-md" />
              {o.title}
            </label>
          </div>
        </Button>
      ))}
      <Button onClick={createTask}>Confirm</Button>
    </>
  );
};

export const CreateTask = ({ createTask }: { createTask: () => void }) => {
  const [step, setStep] = useState<Step>("initial");
  const { t } = useTranslation();

  const closeModal = () => {
    const modal = document.getElementById("create_task_modal") as HTMLDialogElement;
    modal?.close();
  };

  const titles: Record<Step, string> = {
    category: t("leadProfile.selectFromTaskCategory"),
    initial: t("leadProfile.createTask"),
    new: t("leadProfile.createNewTask"),
    select: "select tesks",
  };

  const renderStepContent = () => {
    switch (step) {
      case "new":
        return <AddTaskForm id="create_task_modal" />;
      case "category":
        return <CategoryStep setStep={setStep} />;
      case "select":
        return <SelectTasks createTask={createTask} />;
      default:
        return <InitialStep setStep={setStep} />;
    }
  };

  return (
    <ModalLayout title={titles[step]} step={step} setStep={setStep} closeModal={closeModal}>
      {renderStepContent()}
    </ModalLayout>
  );
};
