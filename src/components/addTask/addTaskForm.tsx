import { useForm } from "@tanstack/react-form";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Button, Textarea } from "../common";
import { FormFields } from "../form";
import type { FormField, FormWithField } from "../form/interface";

interface AddTaskFormValues {
  assignee: string;
  detail: string;
  duedate: string;
  source: string;
  title: string;
}

export const AddTaskForm = ({ id }: { id: string }) => {
  const { t } = useTranslation();

  const formFields: FormField[] = useMemo(
    () => [
      {
        id: "title",
        label: t("addTask.title"),
        placeholder: t("addTask.title"),
        required: true,
        type: "input",
      },
      {
        id: "source",
        inputType: "url",
        label: t("addTask.source"),
        placeholder: t("addTask.source"),
        type: "input",
      },
      {
        id: "duedate",
        label: t("addTask.dueDate"),
        type: "date",
      },
      {
        disabled: true,
        id: "assignee",
        label: t("addTask.assignee"),
        options: [],
        placeholder: t("addTask.selectAssignee"),
        type: "select",
      },
    ],
    [t],
  );

  const form = useForm({
    defaultValues: {
      assignee: "",
      detail: "",
      duedate: "",
      source: "",
      title: "",
    },
    onSubmit: async ({ value }: { value: AddTaskFormValues }) => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));
        // TODO: Replace with actual API call
        // biome-ignore lint/suspicious/noConsole: Development logging
        console.log("Task created:", value);
        handleModalClose();
      } catch (error) {
        // TODO: Replace with proper error handling
        // biome-ignore lint/suspicious/noConsole: Development logging
        console.error("Error creating task:", error);
      }
    },
  });

  const handleModalClose = useCallback(() => {
    const modal = document.getElementById(id) as HTMLDialogElement;
    modal?.close();
    form.reset();
  }, [form, id]);

  const renderField = useCallback(
    (field: FormField) => {
      return <FormFields key={field.id} form={form as FormWithField} field={field} column={3} />;
    },
    [form],
  );

  return (
    <form
      className="flex h-full flex-col gap-4 overflow-hidden p-1"
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="flex flex-col gap-2">{formFields.map((field) => renderField(field))}</div>
      <form.Field name="detail">
        {(fieldApi) => (
          <div className="flex flex-col items-start gap-2">
            <label className="text-label-xs" htmlFor={fieldApi.name}>
              {t("addTask.detail")}
            </label>
            <Textarea
              id={fieldApi.name}
              placeholder={t("addTask.detail")}
              value={fieldApi.state.value}
              onChange={(e) => fieldApi.handleChange(e.target.value)}
              className="resize-none"
            />
          </div>
        )}
      </form.Field>
      <div className="flex justify-between">
        <Button variant="outline" onClick={handleModalClose} className="w-28">
          {t("common.cancel")}
        </Button>
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting]}>
          {([canSubmit, isSubmitting]) => (
            <Button
              className="w-28 place-self-end"
              type="submit"
              disabled={!canSubmit || isSubmitting}
            >
              {isSubmitting ? t("common.loading") : t("common.save")}
            </Button>
          )}
        </form.Subscribe>
      </div>
    </form>
  );
};
