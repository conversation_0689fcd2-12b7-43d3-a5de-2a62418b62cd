import { AssigneeAvatar } from "@components/avatar/assigneeAvatar";
import {
  ChannelBadge,
  PipelineStageBadge,
  QualificationBadge,
  ServiceBadge,
} from "@components/badge";
import { Badge } from "@components/common";
import { CaretDownIcon, XIcon } from "@phosphor-icons/react";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import { FilterForm } from "./filter";
import type { FilterToggleProps, SelectedFilters } from "./interface";

export const FilterToggle = ({
  onApply,
  tasksStatusOptions,
}: FilterToggleProps) => {
  const { t } = useTranslation();

  const [selectedFilters, setSelectedFilters] = useState<SelectedFilters>({
    activeAssinees: [],
    activeBadges: [],
    activeQualifications: [],
    activeServices: [],
    activeSourceChannels: [],
    activeTasksStatuses: [],
  });

  const [draftFilters, setDraftFilters] =
    useState<SelectedFilters>(selectedFilters);

  const handleToggleItem = useCallback(
    (key: keyof SelectedFilters, value: string) => {
      setDraftFilters((prev) => {
        if (!prev) {
          return prev;
        }
        const alreadySelected = prev[key].includes(value);
        const updated = {
          ...prev,
          [key]: alreadySelected
            ? prev[key].filter((v) => v !== value)
            : [...prev[key], value],
        };
        return updated;
      });
    },
    [],
  );

  const handleDrawerClose = useCallback(() => {
    const drawerToggle = document.getElementById(
      "filter-drawer",
    ) as HTMLInputElement;
    if (drawerToggle) {
      drawerToggle.checked = false;
    }
  }, []);

  const handleConfirm = useCallback(() => {
    setSelectedFilters(draftFilters);
    onApply(draftFilters);
    handleDrawerClose();
  }, [draftFilters, onApply, handleDrawerClose]);

  const handleClear = useCallback(() => {
    const cleared: SelectedFilters = {
      activeAssinees: [],
      activeBadges: [],
      activeQualifications: [],
      activeServices: [],
      activeSourceChannels: [],
      activeTasksStatuses: [],
    };
    setDraftFilters(cleared);
    setSelectedFilters(cleared);
    onApply(cleared);
  }, [onApply]);

  const handleRemoveBadge = useCallback(
    (key: keyof SelectedFilters, value: string) => {
      const updatedFilters = {
        ...selectedFilters,
        [key]: selectedFilters[key].filter((v) => v !== value),
      };

      setSelectedFilters(updatedFilters);
      setDraftFilters(updatedFilters);
      onApply(updatedFilters);
    },
    [selectedFilters, onApply],
  );

  const BadgeComponentMap: Record<string, React.ElementType> = {
    activeAssinees: AssigneeAvatar,
    activeBadges: PipelineStageBadge,
    activeContactChannels: ChannelBadge,
    activeOpportunities: QualificationBadge,
    activeServices: ServiceBadge,
  };

  const renderFilterBadge = (key: keyof SelectedFilters, val: string) => {
    const baseProps = {
      children: (
        <button
          type="button"
          className="cursor-pointer"
          onClick={() => handleRemoveBadge(key, val)}
        >
          <XIcon size={14} className="text-base-content" />
        </button>
      ),
      className:
        "inline-flex items-center gap-1 rounded-full border bg-base-200 px-2 py-1",
    };

    const WrapperFilter: React.FC<{
      baseProps: typeof baseProps;
      children?: React.ReactNode;
      divKey: string;
    }> = ({ baseProps, children, divKey }) => {
      return (
        <div key={divKey} className={baseProps.className}>
          {children}
          {baseProps.children}
        </div>
      );
    };

    if (key === "activeAssinees") {
      return (
        <WrapperFilter baseProps={baseProps} divKey={`${key}-${val}`}>
          <AssigneeAvatar image={val} />
        </WrapperFilter>
      );
    }

    if (key === "activeTasksStatuses") {
      const option = tasksStatusOptions.find((o) => o.value === val);
      if (!option) {
        return null;
      }

      return (
        <WrapperFilter baseProps={baseProps} divKey={`${key}-${val}`}>
          {option.label}
        </WrapperFilter>
      );
    }

    const BadgeComp = BadgeComponentMap[key];
    if (BadgeComp) {
      return (
        <WrapperFilter baseProps={baseProps} divKey={`${key}-${val}`}>
          <BadgeComp type={val} />
        </WrapperFilter>
      );
    }

    return (
      <Badge
        type={val}
        config={{
          [val]: {
            containerClasses:
              "bg-base-200 badge-soft badge-neutral border-neutral",
            label: val,
            textClasses: "text-xs text-base-content",
          },
        }}
        {...baseProps}
      />
    );
  };

  return (
    <div className="drawer drawer-end">
      <input id="filter-drawer" type="checkbox" className="drawer-toggle" />
      <div className="drawer-content">
        <div className="flex items-center gap-2">
          {Object.entries(selectedFilters).flatMap(([key, values]) =>
            (values as string[]).map((val) =>
              renderFilterBadge(key as keyof SelectedFilters, val),
            ),
          )}
          <label
            htmlFor="filter-drawer"
            className="btn btn-soft btn-neutural flex w-40 justify-between border-neutral text-info"
          >
            {t("common.filter")}
            <CaretDownIcon className="text-black" size={20} />
          </label>
        </div>
      </div>
      <div className="drawer-side">
        <div className="drawer-overlay" />
        <div className="h-full w-1/2 rounded-l-2xl bg-accent-content p-6 text-base-content">
          <FilterForm
            selectedFilters={draftFilters}
            onToggleItem={handleToggleItem}
            onConfirm={handleConfirm}
            onClear={handleClear}
            onClose={handleDrawerClose}
            tasksStatusOptions={tasksStatusOptions}
          />
        </div>
      </div>
    </div>
  );
};
