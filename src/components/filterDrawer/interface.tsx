export type TaskOption<T extends string = string> = {
  label: React.ReactNode;
  value: T;
};

export type SelectedFilters = {
  activeBadges: string[];
  activeServices: string[];
  activeQualifications: string[];
  activeSourceChannels: string[];
  activeAssinees: string[];
  activeTasksStatuses: string[];
};

export interface FilterFormProps {
  selectedFilters: SelectedFilters;
  tasksStatusOptions: TaskOption[];
  onToggleItem: (key: keyof SelectedFilters, value: string) => void;
  onConfirm: (filters: SelectedFilters) => void;
  onClear: () => void;
  onClose?: () => void;
}

export interface FilterToggleProps {
  onApply: (filters: SelectedFilters) => void;
  tasksStatusOptions: TaskOption[];
}
