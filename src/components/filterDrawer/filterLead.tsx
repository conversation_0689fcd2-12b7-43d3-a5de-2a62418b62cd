import type { SelectedFilters } from "./interface";

export function filterLeads<T>(
  leads: T[],
  filters: SelectedFilters,
  fieldMap: {
    badges: keyof T;
    services: keyof T;
    qualifications: keyof T;
    sourceChannels: keyof T;
    assignees: keyof T;
    tasksStatuses: (item: T) => string;
  },
) {
  return leads.filter((lead) => {
    const matches = [
      filters.activeBadges.includes(String(lead[fieldMap.badges])),
      filters.activeServices.includes(String(lead[fieldMap.services])),
      filters.activeQualifications.includes(String(lead[fieldMap.qualifications])),
      filters.activeSourceChannels.includes(String(lead[fieldMap.sourceChannels])),
      filters.activeAssinees.includes(String(lead[fieldMap.assignees])),
      filters.activeTasksStatuses.includes(fieldMap.tasksStatuses(lead)),
    ];

    const relevantMatches = matches.filter((_, idx) => {
      const lengths = [
        filters.activeBadges.length,
        filters.activeServices.length,
        filters.activeQualifications.length,
        filters.activeSourceChannels.length,
        filters.activeAssinees.length,
        filters.activeTasksStatuses.length,
      ];
      return lengths[idx] > 0;
    });

    if (relevantMatches.length === 0) {
      return true;
    }

    return relevantMatches.some(Boolean);
  });
}
