import { ASSIGNEE_OPTIONS } from "@components/avatar/assigneeAvatar";
import {
  PIPELINE_STAGE_OPTIONS,
  QUALIFICATION_OPTIONS,
  SERVICE_OPTIONS,
  SOURCE_CHANNEL_OPTIONS,
} from "@components/badge";
import { Button } from "@components/common";
import { cn } from "@utils/cn";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import type { FilterFormProps, SelectedFilters } from "./interface";

export const FilterForm: React.FC<FilterFormProps> = ({
  selectedFilters,
  tasksStatusOptions,
  onToggleItem,
  onConfirm,
  onClear,
  onClose,
}) => {
  const { t } = useTranslation();

  const handleConfirmClick = useCallback(() => {
    onConfirm(selectedFilters);
  }, [onConfirm, selectedFilters]);

  const renderBadgeGroup = <T extends { value: string; label: React.ReactNode }>(
    options: T[],
    key: keyof SelectedFilters,
  ) => (
    <div className="collapse-content flex gap-2 text-sm">
      {options.map((type) => (
        <button
          type="button"
          key={type.value}
          className={`cursor-pointer ${
            selectedFilters[key].includes(type.value) ? "opacity-100" : "opacity-40"
          }`}
          onClick={() => onToggleItem(key, type.value)}
        >
          {type.label}
        </button>
      ))}
    </div>
  );

  return (
    <div className="flex h-full flex-col gap-6 p-1">
      <div className="grid grid-cols-1 gap-y-6">
        <div className="collapse-arrow collapse border border-base-300 bg-base-100">
          <input type="checkbox" name="follow-status" />
          <div className="collapse-title font-semibold">{t("followUp.pipelineStage")}</div>
          {renderBadgeGroup(PIPELINE_STAGE_OPTIONS, "activeBadges")}
        </div>
        <div className="collapse-arrow collapse border border-base-300 bg-base-100">
          <input type="checkbox" name="services-interest" />
          <div className="collapse-title font-semibold">{t("followUp.servicesInterest")}</div>
          {renderBadgeGroup(SERVICE_OPTIONS, "activeServices")}
        </div>
        <div className="collapse-arrow collapse border border-base-300 bg-base-100">
          <input type="checkbox" name="qualification" />
          <div className="collapse-title font-semibold">{t("followUp.qualification")}</div>
          {renderBadgeGroup(QUALIFICATION_OPTIONS, "activeQualifications")}
        </div>
        <div className="collapse-arrow collapse border border-base-300 bg-base-100">
          <input type="checkbox" name="contact-channel" />
          <div className="collapse-title font-semibold">{t("followUp.sourceChannel")}</div>
          {renderBadgeGroup(SOURCE_CHANNEL_OPTIONS, "activeSourceChannels")}
        </div>
        <div className="collapse-arrow collapse border border-base-300 bg-base-100">
          <input type="checkbox" name="assignee" />
          <div className="collapse-title font-semibold">{t("followUp.assignee")}</div>
          {renderBadgeGroup(ASSIGNEE_OPTIONS, "activeAssinees")}
        </div>
        <div className="collapse-arrow collapse border border-base-300 bg-base-100">
          <input type="checkbox" name="my-accordion-2" />
          <div className="collapse-title font-semibold">{t("followUp.tasksStatus")}</div>
          {renderBadgeGroup(tasksStatusOptions, "activeTasksStatuses")}
        </div>
      </div>

      <div className="mt-auto mb-5 flex justify-between">
        <Button
          variant="outline"
          className="w-28 hover:border-primary-content/30 hover:bg-primary-content/30"
          type="button"
          onClick={onClose}
        >
          {t("common.cancel")}
        </Button>

        <div className="flex gap-3">
          <Button
            variant="outline"
            className={cn(
              "w-28 border-primary text-primary",
              "hover:border-primary-content hover:bg-primary-content/10 hover:text-primary-content",
            )}
            type="button"
            onClick={onClear}
          >
            {t("common.clear")}
          </Button>

          <Button className="w-28" type="button" onClick={handleConfirmClick}>
            {t("common.confirm")}
          </Button>
        </div>
      </div>
    </div>
  );
};
