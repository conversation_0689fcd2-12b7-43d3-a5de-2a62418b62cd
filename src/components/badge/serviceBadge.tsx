import { Badge, type BadgeConfig } from "@components/common";
import type { ReactNode } from "react";

export type ServiceType = "botox" | "hifu" | "thermage" | "juvelook";

interface ServiceBadgeConfig extends BadgeConfig {
  type: ServiceType;
}

const SERVICE_BADGE_CONFIG: Record<ServiceType, ServiceBadgeConfig> = {
  botox: {
    containerClasses: "bg-success-content",
    label: "Botox",
    textClasses: "text-primary",
    type: "botox",
  },
  hifu: {
    containerClasses: "bg-sky-100",
    label: "Hifu",
    textClasses: "text-sky-500",
    type: "hifu",
  },
  juvelook: {
    containerClasses: "bg-cyan-100",
    label: "Juvelook",
    textClasses: "text-cyan-500",
    type: "juvelook",
  },
  thermage: {
    containerClasses: "bg-purple-100",
    label: "Thermage",
    textClasses: "text-purple-500",
    type: "thermage",
  },
};

interface ServiceBadgeProps {
  type?: ServiceType;
  className?: string;
  children?: ReactNode;
}

export const ServiceBadge = ({ type, className, children }: ServiceBadgeProps) => {
  return (
    <Badge type={type} className={className} config={SERVICE_BADGE_CONFIG}>
      {children}
    </Badge>
  );
};

export const SERVICE_OPTIONS = (["botox", "hifu", "thermage", "juvelook"] as ServiceType[]).map(
  (type) => ({
    label: <ServiceBadge type={type} />,
    value: type,
  }),
);
