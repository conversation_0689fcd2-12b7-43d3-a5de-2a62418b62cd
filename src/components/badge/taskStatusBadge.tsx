import { Badge, type BadgeConfig, type BadgeSize } from "@components/common";
import { cn } from "@utils/cn";
import type { ReactNode } from "react";

export type TaskStatusType = "completed" | "duedate" | "overdue" | "upcoming";

interface TaskStatusBadgeConfig extends BadgeConfig {
  type: TaskStatusType;
}

const TASK_STATUS_BADGE_CONFIG: Record<TaskStatusType, TaskStatusBadgeConfig> =
  {
    completed: {
      containerClasses: "bg-neutral/10 !text-label-xs border border-neutral/20",
      icon: <div className="size-2 rounded-full bg-neutral/50" />,
      iconAlt: "completed",
      label: "COMPLETED",
      textClasses: "text-neutral/70",
      type: "completed",
    },
    duedate: {
      containerClasses:
        "bg-primary/15 !text-label-xs border border-primary/30 shadow-sm",
      icon: <div className="size-2 rounded-full bg-primary" />,
      iconAlt: "duedate",
      label: "DUEDATE",
      textClasses: "text-primary font-semibold",
      type: "duedate",
    },
    overdue: {
      containerClasses: "bg-error/10 !text-label-xs border border-error/[.20]",
      icon: <div className="size-2 rounded-full bg-error/[.50]" />,
      iconAlt: "overdue",
      label: "OVERDUE",
      textClasses: "text-error/[.60]",
      type: "overdue",
    },
    upcoming: {
      containerClasses: "!text-label-xs border border-sky-200/50",
      icon: <div className="size-2 rounded-full bg-sky-300" />,
      iconAlt: "upcoming",
      label: "UPCOMING",
      textClasses: "text-sky-300",
      type: "upcoming",
    },
  };

interface TaskStatusBadgeProps {
  type: TaskStatusType;
  className?: string;
  label?: string;
  size?: BadgeSize;
  children?: ReactNode;
}

export const TaskStatusBadge = ({
  type,
  className,
  label,
  size,
  children,
}: TaskStatusBadgeProps) => {
  const badgeConfig: Record<TaskStatusType, TaskStatusBadgeConfig> = label
    ? {
        ...TASK_STATUS_BADGE_CONFIG,
        [type]: {
          ...TASK_STATUS_BADGE_CONFIG[type],
          label,
        },
      }
    : TASK_STATUS_BADGE_CONFIG;

  return (
    <Badge
      type={type}
      className={cn("rounded-lg", className)}
      config={badgeConfig}
      size={size}
    >
      {children}
    </Badge>
  );
};

export const TASK_STATUS_OPTIONS = (
  ["completed", "duedate", "overdue", "upcoming"] as TaskStatusType[]
).map((type) => ({
  label: <TaskStatusBadge type={type} />,
  value: type,
}));
