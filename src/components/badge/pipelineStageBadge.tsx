import { Badge, type BadgeConfig, type BadgeSize } from "@components/common";

export type PipelineStageType = "pending" | "contacted" | "interested" | "not_interested";

interface PipelineStageBadgeConfig extends BadgeConfig {
  type: PipelineStageType;
}

const PIPELINE_STAGE_BADGE_CONFIG: Record<PipelineStageType, PipelineStageBadgeConfig> = {
  contacted: {
    containerClasses: "border-primary bg-success-content",
    label: "Contacted",
    textClasses: "text-primary",
    type: "contacted",
  },
  interested: {
    containerClasses: "border-secondary bg-rose-50",
    label: "Interested",
    textClasses: "text-error-content",
    type: "interested",
  },
  not_interested: {
    containerClasses: "border-neutral-content bg-base-200",
    label: "Not Interested",
    textClasses: "text-neutral-content",
    type: "not_interested",
  },
  pending: {
    containerClasses: "border-warning-content bg-amber-50",
    label: "Pending",
    textClasses: "text-warning-content",
    type: "pending",
  },
};

interface PipelineStageBadgeProps {
  type?: PipelineStageType;
  className?: string;
  size?: BadgeSize;
}

export const PipelineStageBadge = ({ type, className, size }: PipelineStageBadgeProps) => {
  return (
    <Badge type={type} className={className} config={PIPELINE_STAGE_BADGE_CONFIG} size={size} />
  );
};

export const PIPELINE_STAGE_OPTIONS = (
  ["pending", "contacted", "interested", "not_interested"] as PipelineStageType[]
).map((type) => ({
  label: <PipelineStageBadge type={type} />,
  value: type,
}));
