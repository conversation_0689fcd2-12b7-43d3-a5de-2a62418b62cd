import { Badge, type BadgeConfig, type BadgeSize } from "@components/common";
import { cn } from "@utils/cn";
import type { ReactNode } from "react";

export type LeadStatusType = "active" | "completed" | "draft" | "suspended";

interface LeadStatusBadgeConfig extends BadgeConfig {
  type: LeadStatusType;
}

const LEAD_STATUS_BADGE_CONFIG: Record<LeadStatusType, LeadStatusBadgeConfig> =
  {
    active: {
      containerClasses: "bg-emerald-50 !text-label-xs !border-emerald-300",
      icon: <div className="size-2 rounded-full bg-emerald-400" />,
      iconAlt: "Active",
      label: "Active",
      textClasses: "text-emerald-400",
      type: "active",
    },
    completed: {
      containerClasses: "bg-sky-100 !text-label-xs !border-sky-300",
      icon: <div className="size-2 rounded-full bg-sky-400" />,
      iconAlt: "Completed",
      label: "Completed",
      textClasses: "text-sky-400",
      type: "completed",
    },
    draft: {
      containerClasses: "bg-amber-50 !text-label-xs !border-amber-300",
      icon: <div className="size-2 rounded-full bg-warning-content" />,
      iconAlt: "Draft",
      label: "Draft",
      textClasses: "text-warning-content",
      type: "draft",
    },
    suspended: {
      containerClasses: "bg-base-200",
      icon: <div className="size-2 rounded-full bg-neutral" />,
      iconAlt: "Suspended",
      label: "Suspended",
      textClasses: "text-neutral",
      type: "suspended",
    },
  };

interface LeadStatusBadgeProps {
  type?: LeadStatusType;
  className?: string;
  size?: BadgeSize;
  children?: ReactNode;
}

export const LeadStatusBadge = ({
  type,
  className,
  size,
  children,
}: LeadStatusBadgeProps) => {
  return (
    <Badge
      type={type}
      className={cn("border-neutral", className)}
      config={LEAD_STATUS_BADGE_CONFIG}
      size={size}
    >
      {children}
    </Badge>
  );
};

export const LEAD_STATUS_OPTIONS = (
  ["active", "completed", "draft", "suspended"] as LeadStatusType[]
).map((type) => ({
  label: <LeadStatusBadge type={type} />,
  value: type,
}));
