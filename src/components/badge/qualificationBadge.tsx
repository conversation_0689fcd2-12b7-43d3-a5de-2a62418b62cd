import cloudy from "@assets/cloudy.png";
import moon from "@assets/moon.png";
import sun from "@assets/sun.png";
import { Badge, type BadgeConfig, type BadgeSize } from "@components/common";

export type QualificationType = "hot" | "warm" | "cold";

interface QualificationBadgeConfig extends BadgeConfig {
  type: QualificationType;
}

const QUALIFICATION_BADGE_CONFIG: Record<QualificationType, QualificationBadgeConfig> = {
  cold: {
    containerClasses: "border-primary bg-success-content",
    icon: cloudy,
    iconAlt: "Cloudy",
    label: "COLD",
    textClasses: "text-primary",
    type: "cold",
  },
  hot: {
    containerClasses: "border-secondary bg-rose-50",
    icon: sun,
    iconAlt: "Sun",
    label: "HOT",
    textClasses: "text-error-content",
    type: "hot",
  },
  warm: {
    containerClasses: "border-warning-content bg-amber-50",
    icon: moon,
    iconAlt: "Moon",
    label: "WARM",
    textClasses: "text-warning-content",
    type: "warm",
  },
};

interface QualificationBadgeProps {
  type?: QualificationType;
  className?: string;
  size?: BadgeSize;
}

export const QualificationBadge = ({ type, className, size }: QualificationBadgeProps) => {
  return (
    <Badge type={type} className={className} config={QUALIFICATION_BADGE_CONFIG} size={size} />
  );
};

export const QUALIFICATION_OPTIONS = (["hot", "warm", "cold"] as QualificationType[]).map(
  (type) => ({
    label: <QualificationBadge type={type} />,
    value: type,
  }),
);
