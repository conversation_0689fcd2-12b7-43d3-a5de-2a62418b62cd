import { cn } from "@utils/cn";
import type { FocusEventHandler, ReactNode } from "react";

interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "value"> {
  className?: string;
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  isButton?: boolean;
  onClick?: () => void;
  value?: string | ReactNode;
  variant?: "default" | "transparent";
  isActive?: boolean;
  fontSize?: string;
}

const BASE_CLASSES = "h-fit placeholder:text-info";

const VARIANT_CLASSES: Record<NonNullable<InputProps["variant"]>, string> = {
  default: "bg-base-200 hover:bg-base-200 [&.active]:bg-base-200",
  transparent: "input-ghost",
};

const FONT_SIZE_CLASSES: Record<NonNullable<InputProps["fontSize"]>, string> = {
  "2xl": "text-2xl",
  lg: "text-lg",
  md: "text-md",
  sm: "text-sm",
  xl: "text-xl",
  xs: "text-xs",
};

export const Input = ({
  className,
  prefixIcon,
  suffixIcon,
  isButton = false,
  onClick,
  value,
  variant = "default",
  placeholder,
  isActive = false,
  fontSize = "xs",
  disabled,
  ...props
}: InputProps) => {
  if (isButton) {
    // Extract button-compatible props
    const { onBlur, onFocus, tabIndex, id } = props;

    return (
      <button
        type="button"
        id={id}
        onClick={onClick}
        onBlur={onBlur as unknown as FocusEventHandler<HTMLButtonElement>}
        onFocus={onFocus as unknown as FocusEventHandler<HTMLButtonElement>}
        disabled={disabled}
        tabIndex={tabIndex}
        className={cn(
          VARIANT_CLASSES[variant],
          "!outline-offset-0 flex h-8 w-full cursor-pointer items-center justify-between rounded-lg",
          "border-none px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary",
          isActive && "active",
          className,
        )}
      >
        <div className="flex items-center gap-2">
          {prefixIcon}
          <span className={cn(BASE_CLASSES, FONT_SIZE_CLASSES[fontSize], value ? "" : "text-info")}>
            {value || placeholder}
          </span>
        </div>
        {suffixIcon}
      </button>
    );
  }

  return (
    <label
      className={cn(
        VARIANT_CLASSES[variant],
        "input input-primary !outline-offset-0 h-8 w-full rounded-lg border-none shadow-none",
        isActive && "active",
        disabled && "!bg-transparent",
        className,
      )}
    >
      {prefixIcon}
      <input
        disabled={disabled}
        type="text"
        className={cn(
          BASE_CLASSES,
          FONT_SIZE_CLASSES[fontSize],
          "w-full",
          variant === "transparent" && "transparent-autofill",
          disabled && "!bg-transparent text-base-content/[.75]",
        )}
        placeholder={placeholder}
        value={value as string}
        {...props}
      />
      {suffixIcon}
    </label>
  );
};
