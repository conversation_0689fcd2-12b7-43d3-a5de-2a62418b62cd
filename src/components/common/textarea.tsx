import { cn } from "@utils/cn";

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  variant?: "default" | "transparent";
  className?: string;
}

const VARIANT_CLASSES: Record<NonNullable<TextareaProps["variant"]>, string> = {
  default: "textarea-primary bg-base-200",
  transparent: "textarea-ghost",
};

export const Textarea = ({ className, ...props }: TextareaProps) => {
  return (
    <textarea
      {...props}
      className={cn(
        "textarea h-full w-full rounded-lg border-none text-xs placeholder:text-neutral",
        "focus:outline-none focus:ring-2 focus:ring-primary",
        VARIANT_CLASSES[props.variant ?? "default"],
        className,
      )}
    />
  );
};
