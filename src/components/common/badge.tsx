import { cn } from "@utils/cn";
import type { ReactNode } from "react";

export interface BadgeConfig {
  label?: string;
  icon?: string | ReactNode;
  iconAlt?: string;
  containerClasses?: string;
  textClasses?: string;
}

export type BadgeSize = keyof typeof VARIANT_SIZE;

interface BadgeProps<T extends string> {
  type?: T;
  className?: string;
  config: Record<T, BadgeConfig>;
  children?: ReactNode;
  size?: BadgeSize;
}

const BASE_BADGE_CLASSES = "badge badge-soft badge-primary bg-base-100";
const BASE_TEXT_CLASSES = "text-xs font-semibold";

const VARIANT_SIZE = {
  lg: "badge-lg",
  md: "badge-md",
  sm: "badge-sm",
  xl: "badge-xl",
  xs: "badge-xs",
};

export const Badge = <T extends string>({
  type,
  className,
  config,
  children,
  size = "md",
}: BadgeProps<T>) => {
  if (!type) {
    return null;
  }

  const badgeConfig = config[type];

  const renderIcon = () => {
    if (!badgeConfig.icon) {
      return null;
    }

    if (typeof badgeConfig.icon === "string") {
      return <img src={badgeConfig.icon} alt={badgeConfig.iconAlt || ""} className="size-4" />;
    }
    return <div>{badgeConfig.icon}</div>;
  };

  return (
    <div
      className={cn(
        BASE_BADGE_CLASSES,
        VARIANT_SIZE[size],
        badgeConfig.containerClasses,
        className,
      )}
    >
      {renderIcon()}
      {badgeConfig.label && (
        <span className={cn(BASE_TEXT_CLASSES, badgeConfig.textClasses)}>{badgeConfig.label}</span>
      )}
      {children}
    </div>
  );
};
