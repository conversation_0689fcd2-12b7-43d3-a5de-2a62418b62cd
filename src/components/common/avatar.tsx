import { UserCircleIcon } from "@phosphor-icons/react";
import { cn } from "@utils/cn";
import type { ReactNode } from "react";

interface AvatarProps {
  image?: string;
  className?: string;
  size?: keyof typeof SIZE_CLASSES;
  children?: ReactNode;
}

const SIZE_CLASSES = {
  lg: "w-10",
  md: "w-8",
  sm: "w-6",
  xl: "w-16",
  xs: "w-4",
};

export const Avatar = ({ image, className, size = "md", children }: AvatarProps) => {
  return (
    <div className="avatar">
      <div className={cn("rounded-full", SIZE_CLASSES[size], className)}>
        {image ? (
          <img alt="Avatar" src={image} />
        ) : (
          <UserCircleIcon size={32} weight="fill" className={cn("text-base-300")} />
        )}
      </div>
      {children}
    </div>
  );
};
