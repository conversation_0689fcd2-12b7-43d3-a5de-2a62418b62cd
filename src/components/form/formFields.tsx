import { cn } from "@utils/cn";
import { Input, Select, Textarea } from "../common";
import type { Column, FormFieldsProps } from "./interface";

const COLUMNS_CLASS: Record<Column, { column: string; span: string }> = {
  1: { column: "grid-cols-1", span: "col-span-1" },
  2: { column: "grid-cols-2", span: "col-span-1" },
  3: { column: "grid-cols-3", span: "col-span-2" },
};

export const FormFields = ({ form, field, column }: FormFieldsProps<string>) => {
  const validators = field.required
    ? {
        onChange: ({ value }: { value: string }) =>
          value === undefined ||
          value === null ||
          (typeof value === "string" && value.trim() === "") // handles empty string
            ? `${field.label} is required`
            : undefined,
      }
    : undefined;

  return (
    <form.Field key={field.id} name={field.id} validators={validators}>
      {(fieldApi) => {
        const errors = fieldApi.state.meta.errors;
        const isErrors = Array.isArray(errors) && errors.length > 0;

        return (
          <div className={cn("grid gap-4", COLUMNS_CLASS[column].column)}>
            <label htmlFor={fieldApi.name} className="flex gap-1 self-center whitespace-nowrap">
              <span className="text-label-xs">{field.label}</span>
              {field.required && <span className="text-error text-xs">*</span>}
            </label>

            <div className={cn("relative text-info", COLUMNS_CLASS[column].span)}>
              {field.type === "input" && (
                <Input
                  id={fieldApi.name}
                  type={field.inputType || "text"}
                  placeholder={field.placeholder}
                  value={fieldApi.state.value}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  className={cn("flex-", isErrors ? "outline-1 outline-error" : "")}
                  variant="transparent"
                  disabled={field.disabled}
                />
              )}

              {field.type === "textarea" && (
                <Textarea
                  className={cn("w-full resize-none", isErrors ? "outline-1 outline-error" : "")}
                  id={fieldApi.name}
                  placeholder={field.placeholder}
                  value={fieldApi.state.value}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  variant={field.variant}
                />
              )}

              {field.type === "date" && (
                <Input
                  id={fieldApi.name}
                  type="date"
                  value={fieldApi.state.value}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  className={cn("flex-1", isErrors ? "outline-1 outline-error" : "")}
                  variant="transparent"
                  disabled={field.disabled}
                />
              )}

              {field.type === "select" && (
                <Select
                  id={fieldApi.name}
                  options={field.options}
                  size="sm"
                  variant="popup"
                  value={fieldApi.state.value}
                  onChange={(value) => fieldApi.handleChange(value)}
                  className={cn("flex-1", isErrors ? "outline-1 outline-error" : "")}
                  placeholder={field.placeholder}
                />
              )}

              {isErrors && (
                <div className="-bottom-5 absolute left-1 text-error text-xs">{errors[0]}</div>
              )}
            </div>
          </div>
        );
      }}
    </form.Field>
  );
};
