export type FormField = InputField | SelectField | TextareaField;

export type FieldType = "input" | "select" | "date" | "textarea";

export interface BaseField {
  id: string;
  label: string;
  type: FieldType;
  required?: boolean;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export interface InputField extends BaseField {
  type: "input" | "date";
  inputType?: string;
  defaultValue?: string;
  variant?: "default" | "transparent";
}

export interface SelectField extends BaseField {
  type: "select";
  options: Array<{ label: React.ReactNode; value: string }>;
}

export interface TextareaField extends BaseField {
  type: "textarea";
  variant?: "default" | "transparent";
}

export interface FormValues {
  sourceChannel: string;
  contactInfo: string;
  pipelineStageStatus?: string;
  name?: string;
  note: string;
  qualification: string;
  servicesInterest: string;
  leadStatus: string;
}

export type Column = 1 | 2 | 3;

// FormType
interface FieldApiShape<Value = string> {
  name: string;
  state: {
    value: Value;
    meta: {
      errors: string[];
    };
  };
  handleChange: (next: Value) => void;
}

export interface FormWithField<Value = string> {
  Field: (props: {
    name: string;
    validators?: {
      onChange?: (ctx: { value: Value }) => string | undefined;
    };
    children: (fieldApi: FieldApiShape<Value>) => React.ReactNode;
    key?: React.Key;
  }) => React.ReactNode;
}

export interface FormFieldsProps<Value = string> {
  form: FormWithField<Value>;
  field: FormField;
  column: Column;
}
