import { Avatar } from "@components/common";
import { cn } from "@utils/cn";
import type { ReactNode } from "react";

interface AssigneeAvatarProps {
  image: string;
  className?: string;
  children?: ReactNode;
}

export const ASSIGNEES = [
  "https://img.daisyui.com/images/profile/demo/<EMAIL>",
  "https://img.daisyui.com/images/profile/demo/<EMAIL>",
  "https://img.daisyui.com/images/profile/demo/<EMAIL>",
  "https://img.daisyui.com/images/profile/demo/<EMAIL>",
];

export const AssigneeAvatar = ({ image, className, children }: AssigneeAvatarProps) => {
  return (
    <Avatar image={image} className={cn(className)}>
      {children}
    </Avatar>
  );
};

export const ASSIGNEE_OPTIONS = ASSIGNEES.map((image) => ({
  label: <Avatar image={image} />,
  value: image,
}));
