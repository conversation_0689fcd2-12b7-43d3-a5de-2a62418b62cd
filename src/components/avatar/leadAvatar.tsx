import { LEAD_STATUS_OPTIONS, type LeadStatusType } from "@components/badge";
import { Avatar } from "@components/common";
import { cn } from "@utils/cn";
import { useCallback, useRef, useState } from "react";

interface LeadAvatarProps {
  image: string;
  leadStatus: LeadStatusType;
  onLeadStatusChange?: (status: LeadStatusType) => void;
}

export const LeadAvatar = ({ image, leadStatus, onLeadStatusChange }: LeadAvatarProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = useCallback(() => {
    setIsOpen(!isOpen);
  }, [isOpen]);

  const handleOptionSelect = useCallback(
    (status: LeadStatusType) => {
      onLeadStatusChange?.(status);
      setIsOpen(false);
    },
    [onLeadStatusChange],
  );

  const handleBlur = useCallback(() => {
    setTimeout(() => {
      if (!dropdownRef.current?.contains(document.activeElement)) {
        setIsOpen(false);
      }
    }, 0);
  }, []);

  return (
    <div ref={dropdownRef} className="relative">
      <button
        type="button"
        onClick={toggleDropdown}
        onBlur={handleBlur}
        className="cursor-pointer rounded-full focus:outline-none"
      >
        <Avatar
          className={cn("w-10 ring-2 ring-offset-2 ring-offset-base-100", {
            "ring-emerald-400": leadStatus === "active",
            "ring-neutral": leadStatus === "suspended",
            "ring-sky-600": leadStatus === "completed",
            "ring-warning-content": leadStatus === "draft",
          })}
          image={image}
        />
      </button>
      {isOpen && (
        <div className="absolute top-full left-0 z-50 mt-1 w-48 rounded-lg border border-base-300 bg-base-100 p-2 shadow-lg">
          {LEAD_STATUS_OPTIONS.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleOptionSelect(option.value)}
              className={cn("w-full rounded-lg px-4 py-2 text-left hover:bg-accent-content", {
                "bg-success-content hover:bg-success-content": leadStatus === option.value,
              })}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
