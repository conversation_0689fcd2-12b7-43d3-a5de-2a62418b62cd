import { THEME } from "@enums/theme";
import { MoonIcon } from "@icons/moonIcon";
import { SunIcon } from "@icons/sunIcon";
import { useThemeStore } from "@stores/theme.store";
import type { JSX } from "react";

export const ThemeToggle = (): JSX.Element => {
  const { theme, toggleTheme } = useThemeStore();

  return (
    <label className="flex cursor-pointer items-center gap-2">
      <SunIcon className="opacity-100 transition-opacity dark:opacity-40" />
      <input
        type="checkbox"
        className="toggle theme-controller"
        onChange={toggleTheme}
        checked={theme === THEME.DARK}
        aria-label="Toggle theme"
      />
      <MoonIcon className="opacity-40 transition-opacity dark:opacity-100" />
    </label>
  );
};
