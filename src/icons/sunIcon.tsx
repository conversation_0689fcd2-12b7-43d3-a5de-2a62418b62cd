import { cn } from "@utils/cn";
import type { JSX, SVGProps } from "react";

type Props = SVGProps<SVGSVGElement>;

export const SunIcon = ({ className, ...props }: Props): JSX.Element => (
  <svg
    aria-hidden={true}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={cn("sun-icon", className)}
    {...props}
  >
    <circle cx="12" cy="12" r="5" />
    <path d="M12 1v2M12 21v2M4.2 4.2l1.4 1.4M18.4 18.4l1.4 1.4M1 12h2M21 12h2M4.2 19.8l1.4-1.4M18.4 5.6l1.4-1.4" />
  </svg>
);
