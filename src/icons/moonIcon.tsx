import { cn } from "@utils/cn";
import type { JSX, SVGProps } from "react";

type Props = SVGProps<SVGSVGElement>;

export const MoonIcon = ({ className, ...props }: Props): JSX.Element => (
  <svg
    aria-hidden={true}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={cn("moon-icon", className)}
    {...props}
  >
    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
  </svg>
);
