import { email, type InferInput, minLength, object, pipe, string } from "valibot";

const messages = {
  email: "Invalid email format",
  password: "Password must be at least 6 characters",
};

export const loginSchema: ReturnType<typeof object> = object({
  email: pipe(string(), email(messages.email)),
  password: pipe(string(), minLength(6, messages.password)),
});

export type LoginSchema = InferInput<typeof loginSchema>;
