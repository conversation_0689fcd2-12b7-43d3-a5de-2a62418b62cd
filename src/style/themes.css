@plugin "daisyui/theme" {
  name: "light";
  default: true;
  prefersdark: false;
  color-scheme: light;

  /* Surfaces */
  --color-base-100: #ffffff;
  --color-base-200: #f3f4f6;
  --color-base-300: #e5e7eb;
  --color-base-content: #2c3e50;

  /* Brand colors */
  --color-primary: #4bc7c7;
  --color-primary-content: #a7f3d2;

  --color-secondary: #ff9a8b;
  --color-secondary-content: #ffd6cc;

  --color-accent: #fff4c3;
  --color-accent-content: #fffdf9;

  /* Neutral */
  --color-neutral: #d1d5db;
  --color-neutral-content: #cbd5e0;

  /* State colors */
  --color-info: #7f8c8d;
  --color-info-content: #1f2937;

  --color-success: #38a169;
  --color-success-content: #edfaf9;

  --color-warning: #d69e2e;
  --color-warning-content: #fbbf24;

  --color-error: #e53e3e;
  --color-error-content: #ff6b6b;

  /* Misc design tokens */
  --radius-selector: 1rem;
  --radius-field: 2rem;
  --radius-box: 1rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}
