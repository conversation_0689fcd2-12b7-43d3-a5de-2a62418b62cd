import { isTruthyBoolean } from "@helpers/boolean.helper";
import * as Sentry from "@sentry/react";
import { isProd } from "@utils/env";

export function initSentry(env = import.meta.env) {
  const shouldInit = isTruthyBoolean(env.VITE_SENTRY_IS_USE) && isProd(env.MODE);

  if (!shouldInit) {
    return;
  }

  Sentry.init({
    dsn: env.VITE_SENTRY_DSN,
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.browserProfilingIntegration(),
      Sentry.replayIntegration(),
    ],
    replaysOnErrorSampleRate: 1.0,
    replaysSessionSampleRate: 0.1,
    sendDefaultPii: true,
  });
}
