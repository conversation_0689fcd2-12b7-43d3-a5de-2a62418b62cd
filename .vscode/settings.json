{"editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.organizeImports.biome": "explicit", "source.action.useSortedKeys.biome": "explicit", "source.fixAll.biome": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "biomejs.biome", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "debug.javascript.terminalOptions": {"resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**", "**/node_modules/.vite-temp/**"]}, "javascript.suggest.autoImports": true, "typescript.suggest.autoImports": true, "typescript.tsdk": "node_modules/typescript/lib", "typescript.tsserver.experimental.enableProjectDiagnostics": true, "typescript.tsserver.log": "off", "typescript.tsserver.trace": "off", "tasks.terminateOnExit": true, "biome.enable": true, "biome.enableDiagnostics": true, "biome.enableFormatting": true, "biome.enableCodeActions": true, "biome.enableOrganizeImports": true, "biome.enableLinting": true, "biome.enableTypeChecking": true, "biome.enableAutoFixes": true, "eslint.enable": false, "prettier.enable": false, "tsimporter.enabled": false, "typescript.preferences.importModuleSpecifier": "relative", "javascript.preferences.importModuleSpecifier": "relative", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.validate.enable": true, "javascript.validate.enable": true, "typescript.suggest.paths": true, "javascript.suggest.paths": true}