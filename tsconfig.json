{
  "compilerOptions": {
    "allowImportingTsExtensions": true,
    "allowSyntheticDefaultImports": true,
    /* Base URL */
    "baseUrl": ".",

    /* Module Compatibility */
    "esModuleInterop": true,
    "isolatedModules": true,

    /* React & JSX */
    "jsx": "react-jsx",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleDetection": "force",
    "moduleResolution": "bundler",

    /* Build & Performance */
    "noEmit": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "paths": {
      "@assets/*": ["src/assets/*"],
      "@components/*": ["src/components/*"],
      "@constants/*": ["src/constants/*"],
      "@enums/*": ["src/enums/*"],
      "@helpers/*": ["src/helpers/*"],
      "@i18n/*": ["src/i18n/*"],
      "@icons/*": ["src/icons/*"],
      "@middlewares/*": ["src/middlewares/*"],
      "@pages/*": ["src/pages/*"],
      "@schemas/*": ["src/schemas/*"],
      "@stores/*": ["src/stores/*"],
      "@utils/*": ["src/utils/*"]
    },
    "resolveJsonModule": true,
    "skipLibCheck": true,

    /* Type Checking */
    "strict": true,

    /* Core Configuration */
    "target": "ES2022",
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.tsbuildinfo",

    /* Vite Integration */
    "types": ["vite/client", "vitest", "@testing-library/jest-dom", "@testing-library/react"],
    "useDefineForClassFields": true,
    "verbatimModuleSyntax": true
  },
  "exclude": [".tanstack", "node_modules", "dist"],
  "include": ["src/**/*", "vite.config.ts", "vite-env.d.ts"]
}
