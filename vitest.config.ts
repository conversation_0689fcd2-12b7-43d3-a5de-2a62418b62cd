/// <reference types="vitest" />

import path from "node:path";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig(() => {
  const plugins = [react(), tsconfigPaths()];
  const resolve = {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "@assets": path.resolve(__dirname, "src/assets"),
      "@components": path.resolve(__dirname, "src/components"),
      "@constants": path.resolve(__dirname, "src/constants"),
      "@enums": path.resolve(__dirname, "src/enums"),
      "@helpers": path.resolve(__dirname, "src/helpers"),
      "@i18n": path.resolve(__dirname, "src/i18n"),
      "@icons": path.resolve(__dirname, "src/icons"),
      "@middlewares": path.resolve(__dirname, "src/middlewares"),
      "@pages": path.resolve(__dirname, "src/pages"),
      "@schemas": path.resolve(__dirname, "src/schemas"),
      "@stores": path.resolve(__dirname, "src/stores"),
      "@utils": path.resolve(__dirname, "src/utils"),
    },
  };

  const test = {
    coverage: {
      enabled: true,
      exclude: [
        ".cache",
        ".scannerwork",
        ".vscode",
        ".github",
        ".husky",
        "dist",
        "coverage",
        "public",
        "node_modules",
        "**/*.d.ts",
      ],
      include: ["src/**/*.{ts,tsx}"],
      reporter: ["text", "html", "lcov", "json-summary", "json"],
    },
    environment: "jsdom",
    globals: true,
    setupFiles: "./vitest.setup.ts",
  };

  return {
    plugins,
    resolve,
    test,
  };
});
