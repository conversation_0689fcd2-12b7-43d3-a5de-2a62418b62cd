import "@testing-library/jest-dom/vitest";
import { vi } from "vitest";

globalThis.vi = vi;

globalThis.matchMedia =
  globalThis.matchMedia ||
  (() => ({
    addListener: () => {},
    matches: false,
    removeListener: () => {},
  }));

global.IntersectionObserver = class {
  root = null;
  rootMargin = "";
  thresholds = [];
  observe() {
    return;
  }
  unobserve() {
    return;
  }
  disconnect() {
    return;
  }
  takeRecords() {
    return [];
  }
};

global.ResizeObserver = class {
  observe() {
    return;
  }
  unobserve() {
    return;
  }
  disconnect() {
    return;
  }
};
