import { vi } from "vitest";

export const initBootstrapMockUp = () => {
  vi.mock("react-dom/client", async () => {
    const actual =
      await vi.importActual<typeof import("react-dom/client")>(
        "react-dom/client"
      );

    const renderMock = vi.fn();
    const createRootMock = vi.fn(() => ({
      render: renderMock,
    }));

    return {
      ...actual,
      createRoot: createRootMock,
    };
  });
};
