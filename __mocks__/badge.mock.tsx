import { BadgeConfig } from "../src/components/common";

type TestType =
  | "success"
  | "warning"
  | "error"
  | "icon-success"
  | "icon-warning";

export const TEST_CONFIG: Record<TestType, BadgeConfig> = {
  error: {
    containerClasses: "border-red-500 bg-red-100",
    icon: "error-icon.png",
    iconAlt: "Error Icon",
    label: "ERROR",
    textClasses: "text-red-700",
  },
  "icon-success": {
    containerClasses: "border-green-500 bg-green-100",
    icon: <i className="ri-check-line" />,
    iconAlt: "Success Icon",
    label: "SUCCESS",
    textClasses: "text-green-700",
  },
  "icon-warning": {
    containerClasses: "border-yellow-500 bg-yellow-100",
    icon: <i className="ri-alert-line" />,
    iconAlt: "Warning Icon",
    label: "WARNING",
    textClasses: "text-yellow-700",
  },
  success: {
    containerClasses: "border-green-500 bg-green-100",
    icon: "success-icon.png",
    iconAlt: "Success Icon",
    label: "SUCCESS",
    textClasses: "text-green-700",
  },
  warning: {
    containerClasses: "border-yellow-500 bg-yellow-100",
    icon: "warning-icon.png",
    iconAlt: "Warning Icon",
    label: "WARNING",
    textClasses: "text-yellow-700",
  },
};
