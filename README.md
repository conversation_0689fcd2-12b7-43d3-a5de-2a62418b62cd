# DinoDesk

A modern React application built with the latest technologies.

## Tech Stack

- **Framework**: React 19
- **Language**: TypeScript
- **Build Tool**: Vite
- **Routing**: TanStack Router
- **Forms**: TanStack Form
- **State Management**: TanStack Query (React Query)
- **Styling**: Tailwind CSS v4
- **UI Components**: DaisyUI v5
- **Linting**: Biome
- **Formatting**: Biome

## Getting Started

### Prerequisites

- Bun installed on your machine

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Development

Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run check` - Run Biome

## Project Structure

```
src/
├── page/           # TanStack Router routes
│   ├── index.tsx    # Home page
│   └── about.tsx    # About page
├── main.tsx         # Application entry point
├── index.css        # Global styles
└── routeTree.gen.ts # Generated route tree
```

## Features

- ✅ React 19 with TypeScript
- ✅ TanStack Router for client-side routing
- ✅ TanStack Form for form handling
- ✅ TanStack Query for data fetching
- ✅ Tailwind CSS v4 for styling
- ✅ DaisyUI v5 for UI components
- ✅ Biome for code quality
- ✅ Vite for fast development and building
- ✅ Router devtools in development

## Configuration Files

- `biome.json` - Prettier configuration
- `vite.config.ts` - Vite configuration
# dino-desk
