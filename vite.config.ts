import { sentryVitePlugin } from "@sentry/vite-plugin";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import { visualizer as rollupVisualizer } from "rollup-plugin-visualizer";
import { defineConfig, loadEnv } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { tanstackRouter } from "@tanstack/router-plugin/vite";
import { isTruthyBoolean } from "./src/helpers/boolean.helper";
import { parseEnv } from "./src/utils/env";

export default defineConfig(({ mode }) => {
  const raw = loadEnv(mode, process.cwd(), "");
  const env = parseEnv(raw);
  const plugins = [tanstackRouter(), react(), tailwindcss(), tsconfigPaths()];

  const rollupOptions: {
    output?: { manualChunks?: Record<string, string[]> };
  } = {};
  let terserOptions = {};

  if (env.PROD) {
    terserOptions = {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    };

    plugins.push(
      rollupVisualizer({
        brotliSize: true,
        filename: "dist/stats.html",
        gzipSize: true,
        open: true,
      }),
    );

    rollupOptions.output = {
      manualChunks: {
        i18n: ["i18next", "react-i18next"],
        react: ["react", "react-dom"],
        tanstack: ["@tanstack/react-query", "@tanstack/react-router", "@tanstack/react-form"],
        ui: ["clsx", "tailwind-merge"],
      },
    };
  }

  if (isTruthyBoolean(env.VITE_SENTRY_IS_USE) && env.PROD) {
    plugins.push(
      sentryVitePlugin({
        authToken: env.VITE_SENTRY_AUTH_TOKEN,
        org: env.VITE_SENTRY_ORG,
        project: env.VITE_SENTRY_PROJECT,
        telemetry: env.VITE_SENTRY_TELEMETRY,
      }),
    );
  }

  return {
    build: {
      chunkSizeWarningLimit: 700,
      minify: env.PROD ? "terser" : undefined,
      rollupOptions,
      sourcemap: true,
      terserOptions,
    },
    plugins,
  };
});
