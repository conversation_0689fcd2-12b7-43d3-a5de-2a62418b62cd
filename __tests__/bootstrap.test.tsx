
import { describe, it, expect, beforeEach, vi } from "vitest";
import { initBootstrapMockUp } from "../__mocks__/bootstrap.mock";

initBootstrapMockUp();

beforeEach(() => {
  vi.resetModules();
});

describe("bootstrapApp", () => {
  it("throws error if #root is not found", async () => {
    document.body.innerHTML = ""; // No #root

    await expect(import("../src/main")).rejects.toThrowError(
      'Root element with id "root" not found'
    );
  }, 60000);

  it("calls createRoot and renders the app", async () => {
    const rootDiv = document.createElement("div");
    rootDiv.id = "root";
    document.body.appendChild(rootDiv);

    const { createRoot } = await import("react-dom/client");

    await import("../src/main");

    expect(createRoot).toHaveBeenCalledWith(rootDiv);
    expect(createRoot(rootDiv).render).toHaveBeenCalled(); 
  });
});