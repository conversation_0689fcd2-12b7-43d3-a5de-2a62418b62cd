import { describe, it, expect } from "vitest";
import { isTruthyBoolean } from "@helpers/boolean.helper";

describe("isTruthyBoolean", () => {
  it("should return true for boolean true", () => {
    expect(isTruthyBoolean(true)).toBe(true);
  });

  it("should return false for boolean false", () => {
    expect(isTruthyBoolean(false)).toBe(false);
  });

  it("should return true for string 'true'", () => {
    expect(isTruthyBoolean("true")).toBe(true);
  });

  it("should return true for string ' TRUE ' with whitespace and casing", () => {
    expect(isTruthyBoolean(" TRUE ")).toBe(true);
  });

  it("should return false for string 'false'", () => {
    expect(isTruthyBoolean("false")).toBe(false);
  });

  it("should return false for string 'yes'", () => {
    expect(isTruthyBoolean("yes")).toBe(false);
  });

  it("should return false for string '1'", () => {
    expect(isTruthyBoolean("1")).toBe(false); // strict match
  });

  it("should return false for number 1", () => {
    expect(isTruthyBoolean(1)).toBe(false);
  });

  it("should return false for null", () => {
    expect(isTruthyBoolean(null)).toBe(false);
  });

  it("should return false for undefined", () => {
    expect(isTruthyBoolean(undefined)).toBe(false);
  });

  it("should return false for an object", () => {
    expect(isTruthyBoolean({})).toBe(false);
  });
});
