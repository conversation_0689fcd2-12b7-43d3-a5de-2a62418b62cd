import { describe, it, expect } from "vitest";
import { removeTrailingSlash } from "@helpers/path.helper";

describe("removeTrailingSlash", () => {
  it("should remove trailing slash from a standard path", () => {
    expect(removeTrailingSlash("/about/")).toBe("/about");
  });

  it("should return the same path if there is no trailing slash", () => {
    expect(removeTrailingSlash("/contact")).toBe("/contact");
  });

  it("should not remove slash from root path", () => {
    expect(removeTrailingSlash("/")).toBe("/");
  });

  it("should remove trailing slash from multi-level path", () => {
    expect(removeTrailingSlash("/blog/posts/")).toBe("/blog/posts");
  });

  it("should return empty string unchanged", () => {
    expect(removeTrailingSlash("")).toBe("");
  });

  it("should handle single-character paths other than '/'", () => {
    expect(removeTrailingSlash("a")).toBe("a");
  });

  it("should handle paths with query parameters", () => {
    expect(removeTrailingSlash("/search/?q=test")).toBe("/search/?q=test");
  });
});
