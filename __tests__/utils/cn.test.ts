import { describe, it, expect } from "vitest";
import { type ClassValue } from "clsx";
import { cn } from "@utils/cn";

describe("cn utility function", () => {
  it("should concatenate basic class names", () => {
    const result = cn("text-base", "font-bold");
    expect(result).toBe("text-base font-bold");
  });

  it("should eliminate falsy values (false, null, undefined)", () => {
    const result = cn("text-sm", false, null, undefined, "bg-white");
    expect(result).toBe("text-sm bg-white");
  });

  it("should handle conditional class names", () => {
    const isActive = true;
    const result = cn("text-sm", isActive && "text-red-500");
    expect(result).toBe("text-sm text-red-500");
  });

  it("should ignore conditional class names that resolve to false", () => {
    const isActive = false;
    const result = cn("text-sm", isActive && "text-red-500");
    expect(result).toBe("text-sm");
  });

  it("should remove duplicate class names", () => {
    const result = cn("p-4", "p-4", "p-4");
    expect(result).toBe("p-4");
  });

  it("should override conflicting Tailwind classes", () => {
    const result = cn("p-2", "p-4");
    expect(result).toBe("p-4"); // twMerge keeps last conflict
  });

  it("should work with clsx-style conditional objects", () => {
    const result = cn("text-lg", {
      "text-red-500": true,
      "text-blue-500": false,
    });
    expect(result).toBe("text-lg text-red-500");
  });

  it("should handle arrays of class names", () => {
    const result = cn(["text-sm", "text-red-500"], "p-2");
    expect(result).toBe("text-sm text-red-500 p-2");
  });

  it("should handle nested arrays and falsy values", () => {
    const result = cn(["text-sm", [false, "bg-white"]], null, "rounded");
    expect(result).toBe("text-sm bg-white rounded");
  });

  it("should return empty string for no arguments", () => {
    const result = cn();
    expect(result).toBe("");
  });

  it("should return empty string for only falsy values", () => {
    const result = cn(false, null, undefined, "", 0 as unknown as ClassValue);
    expect(result).toBe("");
  });
});
