import { describe, it, vi, expect, beforeEach } from "vitest";
import {init} from "@sentry/react";
import { mockBaseEnv} from "../__mocks__/instrument.mock";
import { initSentry } from '../src/instrument';

vi.mock("@sentry/react", () => ({
  init: vi.fn(),
  browserTracingIntegration: vi.fn(() => "tracing"),
  browserProfilingIntegration: vi.fn(() => "profiling"),
  replayIntegration: vi.fn(() => "replay"),
}));

describe("initSentry", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should initialize Sentry when IS_USE is true and mode is production", () => {
    initSentry(mockBaseEnv);
    expect(init).toHaveBeenCalled();
  });

  it("should not initialize Sentry if IS_USE is false", () => {
    initSentry({ ...mockBaseEnv, VITE_SENTRY_IS_USE: "false" });
    expect(init).not.toHaveBeenCalled();
  });

  it("should not initialize Sentry if mode is not production", () => {
    initSentry({ ...mockBaseEnv, MODE: "development" });
    expect(init).not.toHaveBeenCalled();
  });

  it("should not throw if init is skipped", () => {
    expect(() =>
      initSentry({ ...mockBaseEnv, VITE_SENTRY_IS_USE: "false", MODE: "development" })
    ).not.toThrow();
  });
});