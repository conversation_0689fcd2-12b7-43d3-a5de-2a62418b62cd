import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import "@testing-library/jest-dom/vitest";
import { AddTaskForm } from "../../../src/components/addTask/addTaskForm";

// Mock the translation hook
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        "addTask.title": "Title",
        "addTask.detail": "Detail",
        "addTask.source": "Source",
        "addTask.dueDate": "Due Date",
        "addTask.assignee": "Assignee",
        "addTask.selectAssignee": "Select Assignee",
        "common.cancel": "Cancel",
        "common.save": "Save",
        "common.loading": "Loading...",
      };
      return translations[key] || key;
    },
  }),
}));

// Mock the modal close functionality
Object.defineProperty(document, "getElementById", {
  value: vi.fn((id: string) => {
    if (id === "add_task_modal") {
      return {
        close: vi.fn(),
      };
    }
    return null;
  }),
  writable: true,
});

describe.skip("AddTaskForm Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should render all form fields correctly", () => {
    render(<AddTaskForm id="1" />);

    // Check if all form fields are rendered
    expect(screen.getByLabelText(/title/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/detail/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/source/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/due date/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/assignee/i)).toBeInTheDocument();

    // Check if buttons are rendered
    expect(screen.getByRole("button", { name: /cancel/i })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /save/i })).toBeInTheDocument();
  });

  it("should show required field indicator for title", () => {
    render(<AddTaskForm id="1" />);

    const titleLabel = screen.getByText("Title").closest("label");
    expect(titleLabel).toContainHTML(
      '<span class="text-error text-h6">*</span>',
    );
  });

  it("should handle form input changes", () => {
    render(<AddTaskForm id="1" />);

    const titleInput = screen.getByLabelText(/title/i) as HTMLInputElement;
    const detailTextarea = screen.getByLabelText(
      /detail/i,
    ) as HTMLTextAreaElement;
    const sourceInput = screen.getByLabelText(/source/i) as HTMLInputElement;

    fireEvent.change(titleInput, { target: { value: "Test Task" } });
    fireEvent.change(detailTextarea, { target: { value: "Test Description" } });
    fireEvent.change(sourceInput, { target: { value: "https://example.com" } });

    expect(titleInput.value).toBe("Test Task");
    expect(detailTextarea.value).toBe("Test Description");
    expect(sourceInput.value).toBe("https://example.com");
  });

  it("should show validation error for required title field", async () => {
    render(<AddTaskForm id="1" />);

    const titleInput = screen.getByLabelText(/title/i);
    const saveButton = screen.getByRole("button", { name: /save/i });

    // Try to submit without filling required field
    fireEvent.change(titleInput, { target: { value: "" } });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText("Title is required")).toBeInTheDocument();
    });
  });

  // it("should handle assignee selection", async () => {
  //   render(<AddTaskForm />);

  //   const assigneeSelect = screen.getByRole("button", { name: /assignee/i });
  //   fireEvent.click(assigneeSelect);

  //   await waitFor(() => {
  //     expect(screen.getByText("John Doe")).toBeInTheDocument();
  //   });

  //   fireEvent.click(screen.getByText("John Doe"));

  //   await waitFor(() => {
  //     expect(screen.getByText("John Doe")).toBeInTheDocument();
  //   });
  // });

  it("should handle cancel button click", () => {
    const mockClose = vi.fn();
    document.getElementById = vi.fn().mockReturnValue({ close: mockClose });

    render(<AddTaskForm id="1" />);

    const cancelButton = screen.getByRole("button", { name: /cancel/i });
    fireEvent.click(cancelButton);

    expect(mockClose).toHaveBeenCalled();
  });

  it("should show form validation behavior", async () => {
    render(<AddTaskForm id="1" />);

    const saveButton = screen.getByRole("button", { name: /save/i });

    expect(saveButton).not.toBeDisabled();
  });

  it("should enable save button when required fields are filled", async () => {
    render(<AddTaskForm id="1" />);

    const titleInput = screen.getByLabelText(/title/i);
    const saveButton = screen.getByRole("button", { name: /save/i });

    fireEvent.change(titleInput, { target: { value: "Test Task" } });

    await waitFor(() => {
      expect(saveButton).not.toBeDisabled();
    });
  });

  it("should show loading state during form submission", async () => {
    render(<AddTaskForm id="1" />);

    const titleInput = screen.getByLabelText(/title/i);
    const saveButton = screen.getByRole("button", { name: /save/i });

    fireEvent.change(titleInput, { target: { value: "Test Task" } });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText("Loading...")).toBeInTheDocument();
    });
  });

  it("should use proper input types", () => {
    render(<AddTaskForm id="1" />);

    const sourceInput = screen.getByLabelText(/source/i);
    const dueDateInput = screen.getByLabelText(/due date/i);

    expect(sourceInput).toHaveAttribute("type", "url");
    expect(dueDateInput).toHaveAttribute("type", "date");
  });

  it("should apply proper grid layout classes", () => {
    const { container } = render(<AddTaskForm id="1" />);

    const formElement = container.querySelector("form");
    expect(formElement).toHaveClass(
      "flex",
      "h-full",
      "flex-col",
      "gap-4",
      "overflow-hidden",
      "p-1",
    );
  });
});
