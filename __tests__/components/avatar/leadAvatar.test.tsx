import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import { LeadAvatar } from "../../../src/components/avatar/leadAvatar";

describe.skip("LeadAvatar Component", () => {
  const mockProps = {
    image: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
    leadStatus: "active" as const,
    onLeadStatusChange: vi.fn(),
  };

  it("should render avatar with correct ring color for active status", () => {
    const { container } = render(<LeadAvatar {...mockProps} />);
    const avatar = container.querySelector(".ring-emerald-400");
    expect(avatar).toBeInTheDocument();
  });

  it("should render avatar with correct ring color for completed status", () => {
    const { container } = render(
      <LeadAvatar {...mockProps} leadStatus="completed" />,
    );
    const avatar = container.querySelector(".ring-sky-600");
    expect(avatar).toBeInTheDocument();
  });

  it("should render avatar with correct ring color for draft status", () => {
    const { container } = render(
      <LeadAvatar {...mockProps} leadStatus="draft" />,
    );
    const avatar = container.querySelector(".ring-warning-content");
    expect(avatar).toBeInTheDocument();
  });

  it("should render avatar with correct ring color for suspended status", () => {
    const { container } = render(
      <LeadAvatar {...mockProps} leadStatus="suspended" />,
    );
    const avatar = container.querySelector(".ring-neutral");
    expect(avatar).toBeInTheDocument();
  });

  it("should show dropdown when avatar is clicked", () => {
    render(<LeadAvatar {...mockProps} />);
    const avatarButton = screen.getByRole("button");
    fireEvent.click(avatarButton);

    // Check if the dropdown with status options is visible
    const activeOption = screen.getByText("ACTIVE");
    const completedOption = screen.getByText("COMPLETED");
    const draftOption = screen.getByText("DRAFT");
    const suspendedOption = screen.getByText("SUSPENDED");

    expect(activeOption).toBeInTheDocument();
    expect(completedOption).toBeInTheDocument();
    expect(draftOption).toBeInTheDocument();
    expect(suspendedOption).toBeInTheDocument();
  });

  it("should call onLeadStatusChange when status is changed", () => {
    const onLeadStatusChange = vi.fn();
    render(
      <LeadAvatar {...mockProps} onLeadStatusChange={onLeadStatusChange} />,
    );

    const avatarButton = screen.getByRole("button");
    fireEvent.click(avatarButton);

    const completedOption = screen.getByText("COMPLETED");
    fireEvent.click(completedOption);

    expect(onLeadStatusChange).toHaveBeenCalledWith("completed");
  });

  it("should close dropdown after selecting an option", () => {
    const onLeadStatusChange = vi.fn();
    render(
      <LeadAvatar {...mockProps} onLeadStatusChange={onLeadStatusChange} />,
    );

    const avatarButton = screen.getByRole("button");
    fireEvent.click(avatarButton);

    // Dropdown should be open
    expect(screen.getByText("COMPLETED")).toBeInTheDocument();

    const completedOption = screen.getByText("COMPLETED");
    fireEvent.click(completedOption);

    // Dropdown should be closed
    expect(screen.queryByText("COMPLETED")).not.toBeInTheDocument();
  });

  it("should not call onLeadStatusChange when prop is not provided", () => {
    const { container } = render(
      <LeadAvatar image={mockProps.image} leadStatus={mockProps.leadStatus} />,
    );
    const avatarButton = screen.getByRole("button");
    fireEvent.click(avatarButton);

    const completedOption = screen.getByText("COMPLETED");
    fireEvent.click(completedOption);

    // Should not throw error when onLeadStatusChange is undefined
    expect(container).toBeInTheDocument();
  });
});
