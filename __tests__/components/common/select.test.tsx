import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import { Select } from "../../../src/components/common";
import { mockOptions } from "../../../__mocks__/select.mock";

describe("Select Component", () => {
  it("should render select correctly", () => {
    render(<Select placeholder="Choose option" />);
    expect(screen.getByRole("button")).toBeInTheDocument();
    expect(screen.getByText("Choose option")).toBeInTheDocument();
  });

  it("should render with default options when no options provided", () => {
    render(<Select />);
    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(screen.getByText("Yes")).toBeInTheDocument();
    expect(screen.getByText("No")).toBeInTheDocument();
    expect(screen.getAllByRole("button")).toHaveLength(3);
  });

  it("should display custom options when provided", () => {
    render(<Select options={mockOptions} />);
    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(screen.getByText("Option 1")).toBeInTheDocument();
    expect(screen.getByText("Option 2")).toBeInTheDocument();
    expect(screen.getByText("Option 3")).toBeInTheDocument();
    expect(screen.getAllByRole("button")).toHaveLength(4);
  });

  it("should show selected value", () => {
    render(<Select options={mockOptions} value="option2" />);
    expect(screen.getByText("Option 2")).toBeInTheDocument();
  });

  it("should call onChange when an option is selected", () => {
    const onChange = vi.fn();
    render(<Select options={mockOptions} onChange={onChange} />);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    const option1 = screen.getByText("Option 1");
    fireEvent.click(option1);

    expect(onChange).toHaveBeenCalledWith("option1");
  });

  it("should close dropdown after selecting an option", () => {
    render(<Select options={mockOptions} />);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(screen.getByText("Option 1")).toBeInTheDocument();

    fireEvent.click(screen.getByText("Option 1"));

    expect(screen.queryByText("Option 1")).not.toBeInTheDocument();
  });

  it("should close dropdown on blur", async () => {
    render(<Select options={mockOptions} />);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(screen.getByText("Option 1")).toBeInTheDocument();

    fireEvent.blur(button);

    await waitFor(() => {
      expect(screen.queryByText("Option 1")).not.toBeInTheDocument();
    });
  });

  it("should apply custom className", () => {
    const { container } = render(<Select className="custom-select" />);
    const selectInput = container.querySelector(".custom-select");
    expect(selectInput).toBeInTheDocument();
  });

  it("should handle different sizes", () => {
    const { rerender, container } = render(<Select size="sm" />);
    let selectInput = container.querySelector(".h-8");
    expect(selectInput).toBeInTheDocument();

    rerender(<Select size="md" />);
    selectInput = container.querySelector(".h-9");
    expect(selectInput).toBeInTheDocument();

    rerender(<Select size="lg" />);
    selectInput = container.querySelector(".h-10");
    expect(selectInput).toBeInTheDocument();
  });

  it("should handle popup variant", () => {
    render(<Select variant="popup" />);
    const button = screen.getByRole("button");

    expect(button.querySelector(".select-icon")).not.toBeInTheDocument();
  });

  it("should show placeholder when no value selected", () => {
    render(<Select placeholder="Select an option" />);
    expect(screen.getByText("Select an option")).toBeInTheDocument();
  });

  it("should toggle dropdown on multiple clicks", () => {
    render(<Select options={mockOptions} />);
    const button = screen.getByRole("button");

    // First click - open
    fireEvent.click(button);
    expect(screen.getByText("Option 1")).toBeInTheDocument();

    // Second click - close
    fireEvent.click(button);
    expect(screen.queryByText("Option 1")).not.toBeInTheDocument();

    // Third click - open again
    fireEvent.click(button);
    expect(screen.getByText("Option 1")).toBeInTheDocument();
  });
});
