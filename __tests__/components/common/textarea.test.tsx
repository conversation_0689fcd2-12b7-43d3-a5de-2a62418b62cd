import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import { Textarea } from "../../../src/components/common";

describe("Textarea Component", () => {
  it("should render textarea correctly", () => {
    render(<Textarea placeholder="Type here" />);

    expect(screen.getByPlaceholderText("Type here")).toBeInTheDocument();
    expect(screen.getByRole("textbox")).toBeInTheDocument();
  });

  it("should render as textarea element", () => {
    render(<Textarea />);
    const textarea = screen.getByRole("textbox");

    expect(textarea.tagName).toBe("TEXTAREA");
  });

  it("should apply base classes correctly", () => {
    const { container } = render(<Textarea placeholder="Type here" />);
    const textarea = container.firstChild as HTMLElement;

    expect(textarea).toHaveClass(
      "h-full",
      "rounded-lg",
      "border-none",
      "text-xs"
    );
  });

  it("should apply focus classes correctly", () => {
    const { container } = render(<Textarea />);
    const textarea = container.firstChild as HTMLElement;

    expect(textarea).toHaveClass(
      "focus:outline-none",
      "focus:ring-2",
      "focus:ring-primary"
    );
  });

  it("should apply custom className", () => {
    const { container } = render(<Textarea className="custom-class" />);
    const textarea = container.firstChild as HTMLElement;

    expect(textarea).toHaveClass("custom-class");
    expect(textarea).toHaveClass("textarea", "textarea-primary");
  });

  it("should handle user input correctly", () => {
    render(<Textarea placeholder="Enter text" />);
    const textarea = screen.getByRole("textbox");

    fireEvent.change(textarea, { target: { value: "Hello World" } });
    expect(textarea).toHaveValue("Hello World");
  });

  it("should handle controlled value", () => {
    const { rerender } = render(
      <Textarea value="Initial text" onChange={() => {}} />
    );
    const textarea = screen.getByRole("textbox");

    expect(textarea).toHaveValue("Initial text");

    rerender(<Textarea value="Updated text" onChange={() => {}} />);
    expect(textarea).toHaveValue("Updated text");
  });

  it("should handle onChange events", () => {
    const handleChange = vi.fn();
    render(<Textarea onChange={handleChange} />);
    const textarea = screen.getByRole("textbox");

    fireEvent.change(textarea, { target: { value: "test input" } });

    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          value: "test input",
        }),
      })
    );
  });

  it("should handle disabled state", () => {
    render(<Textarea disabled placeholder="Disabled textarea" />);
    const textarea = screen.getByRole("textbox");

    expect(textarea).toBeDisabled();
  });

  it("should handle readonly state", () => {
    render(<Textarea readOnly value="Read only text" />);
    const textarea = screen.getByRole("textbox");

    expect(textarea).toHaveAttribute("readonly");
    expect(textarea).toHaveValue("Read only text");
  });

  it("should pass through HTML textarea attributes", () => {
    render(
      <Textarea
        id="test-textarea"
        data-testid="custom-textarea"
        rows={5}
        cols={30}
        maxLength={100}
        placeholder="Test"
      />
    );

    const textarea = screen.getByRole("textbox");
    expect(textarea).toHaveAttribute("id", "test-textarea");
    expect(textarea).toHaveAttribute("data-testid", "custom-textarea");
    expect(textarea).toHaveAttribute("rows", "5");
    expect(textarea).toHaveAttribute("cols", "30");
    expect(textarea).toHaveAttribute("maxLength", "100");
  });

  it("should handle required attribute", () => {
    render(<Textarea required placeholder="Required field" />);
    const textarea = screen.getByRole("textbox");

    expect(textarea).toBeRequired();
  });

  it("should handle multiline text correctly", () => {
    const multilineText = "Line 1\nLine 2\nLine 3";
    render(<Textarea value={multilineText} onChange={() => {}} />);
    const textarea = screen.getByRole("textbox");

    expect(textarea).toHaveValue(multilineText);
  });

  it("should handle resize property", () => {
    const { container } = render(<Textarea style={{ resize: "none" }} />);
    const textarea = container.firstChild as HTMLElement;

    expect(textarea).toHaveStyle({ resize: "none" });
  });

  it("should combine custom className with base classes", () => {
    const { container } = render(
      <Textarea className="my-custom-class h-32" placeholder="Custom styled" />
    );
    const textarea = container.firstChild as HTMLElement;

    expect(textarea).toHaveClass("my-custom-class", "h-32");
    expect(textarea).toHaveClass("textarea", "textarea-primary", "bg-base-200");
  });
});
