{"dependencies": {"@phosphor-icons/react": "^2.1.10", "@sentry/react": "^9.41.0", "@tanstack/react-form": "^1.14.2", "@tanstack/react-query": "^5.83.0", "@tanstack/react-router": "^1.129.8", "@tanstack/react-router-devtools": "^1.129.8", "@types/qs": "^6.14.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "husky": "^8.0.0", "i18next": "^25.3.2", "ky": "^1.8.2", "qs": "^6.14.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.1", "remixicon": "^4.6.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animated": "^2.0.0", "valibot": "^1.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@sentry/vite-plugin": "^4.0.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/router-cli": "^1.129.8", "@tanstack/router-devtools": "^1.129.8", "@tanstack/router-plugin": "^1.129.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jsdom": "^21.1.7", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "daisyui": "^5.0.46", "globals": "^16.3.0", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "rollup-plugin-visualizer": "^6.0.3", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "engines": {"node": "24", "npm": ">=6.0.0"}, "name": "dino-desk", "private": true, "scripts": {"build": "tsc -b && vite build", "check": "biome check --write --unsafe", "dev": "vite", "format": "biome format --write .", "lint": "biome lint --write .", "prepare": "husky install", "preview": "vite preview", "test": "vitest ", "test:coverage": "vitest --coverage", "tsr:generate": "tsr generate"}, "type": "module", "version": "0.0.1"}